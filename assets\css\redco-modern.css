/**
 * RedCo Optimizer - Clean Professional Interface
 * Inspired by <PERSON><PERSON>, Linear, and modern SaaS dashboards
 * Version: 5.0.0
 */

/* ===== CLEAN FOUNDATION ===== */
.redco-optimizer-modules {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
    background: #fafbfc !important;
    color: #1f2937 !important;
    line-height: 1.5 !important;
    -webkit-font-smoothing: antialiased !important;
}

.redco-optimizer-modules .wrap {
    background: #fafbfc !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
}

/* ===== CLEAN HEADER ===== */
.redco-module-tab {
    background: #fafbfc !important;
    padding: 32px !important;
    min-height: 100vh !important;
}

/* Create clean header */
.redco-module-tab::before {
    content: '';
    position: fixed !important;
    top: 32px !important;
    left: 160px !important;
    right: 0 !important;
    height: 80px !important;
    background: white !important;
    border-bottom: 1px solid #e5e7eb !important;
    z-index: 1000 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.redco-module-tab::after {
    content: '⚡ RedCo Optimizer';
    position: fixed !important;
    top: 32px !important;
    left: 160px !important;
    right: 0 !important;
    height: 80px !important;
    display: flex !important;
    align-items: center !important;
    padding: 0 32px !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    z-index: 1001 !important;
    background: white !important;
}

/* Push content below header */
.redco-module-tab > * {
    margin-top: 100px !important;
}

/* ===== CLEAN CARDS ===== */
.redco-module-tab .form-table,
.redco-module-tab > div:not(.module-header-section) {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    margin: 0 0 24px 0 !important;
    padding: 24px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: box-shadow 0.2s ease !important;
}

.redco-module-tab .form-table:hover,
.redco-module-tab > div:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* ===== CLEAN TYPOGRAPHY ===== */
.redco-module-tab h1,
.redco-module-tab h2,
.redco-module-tab h3,
.redco-module-tab h4 {
    color: #1f2937 !important;
    font-weight: 600 !important;
    margin-bottom: 16px !important;
    line-height: 1.3 !important;
}

.redco-module-tab h2 {
    font-size: 20px !important;
    color: #111827 !important;
    margin-bottom: 20px !important;
}

.redco-module-tab h3 {
    font-size: 18px !important;
    color: #374151 !important;
}

.redco-module-tab p,
.redco-module-tab label {
    color: #4b5563 !important;
    line-height: 1.6 !important;
    margin-bottom: 12px !important;
}

.redco-module-tab .description {
    color: #6b7280 !important;
    font-size: 14px !important;
    margin-top: 6px !important;
    line-height: 1.5 !important;
}

/* ===== CLEAN FORM ELEMENTS ===== */
.redco-module-tab input[type="text"],
.redco-module-tab input[type="number"],
.redco-module-tab input[type="email"],
.redco-module-tab select,
.redco-module-tab textarea {
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    color: #1f2937 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.redco-module-tab input[type="text"]:focus,
.redco-module-tab input[type="number"]:focus,
.redco-module-tab input[type="email"]:focus,
.redco-module-tab select:focus,
.redco-module-tab textarea:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

/* ===== CLEAN BUTTONS ===== */
.redco-module-tab .button-primary {
    background: #3b82f6 !important;
    border: none !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.redco-module-tab .button-primary:hover {
    background: #2563eb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.redco-module-tab .button-secondary {
    background: white !important;
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
}

.redco-module-tab .button-secondary:hover {
    border-color: #9ca3af !important;
    background: #f9fafb !important;
}

/* ===== CLEAN FORM TABLES ===== */
.redco-module-tab .form-table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
    width: 100% !important;
}

.redco-module-tab .form-table th {
    background: #f9fafb !important;
    padding: 16px 20px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    border-bottom: 1px solid #e5e7eb !important;
    border-right: 1px solid #e5e7eb !important;
    width: 200px !important;
    vertical-align: top !important;
    font-size: 14px !important;
}

.redco-module-tab .form-table td {
    background: white !important;
    padding: 16px 20px !important;
    border-bottom: 1px solid #e5e7eb !important;
    vertical-align: top !important;
}

.redco-module-tab .form-table tr:last-child th,
.redco-module-tab .form-table tr:last-child td {
    border-bottom: none !important;
}

/* ===== CLEAN CHECKBOXES ===== */
.redco-module-tab input[type="checkbox"] {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    accent-color: #3b82f6 !important;
}

/* ===== HIDE WORDPRESS CLUTTER ===== */
.redco-module-tab .wp-header-end,
.redco-module-tab .screen-reader-text,
.redco-module-tab .update-nag {
    display: none !important;
}

/* ===== CLEAN NOTICES ===== */
.redco-module-tab .notice,
.redco-module-tab .updated,
.redco-module-tab .error {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-left: 4px solid #3b82f6 !important;
    border-radius: 8px !important;
    margin: 0 0 20px 0 !important;
    padding: 12px 16px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    color: #374151 !important;
}

.redco-module-tab .notice.notice-success,
.redco-module-tab .updated {
    border-left-color: #10b981 !important;
}

.redco-module-tab .notice.notice-error,
.redco-module-tab .error {
    border-left-color: #ef4444 !important;
}

/* ===== CLEAN LINKS ===== */
.redco-module-tab a {
    color: #3b82f6 !important;
    text-decoration: none !important;
    transition: color 0.2s ease !important;
}

.redco-module-tab a:hover {
    color: #2563eb !important;
    text-decoration: underline !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .redco-module-tab {
        padding: 16px !important;
    }

    .redco-module-tab::after {
        font-size: 18px !important;
        padding: 0 16px !important;
    }

    .redco-module-tab .form-table th,
    .redco-module-tab .form-table td {
        padding: 12px !important;
        display: block !important;
        width: 100% !important;
        border-right: none !important;
    }
}


