<?php
/**
 * API Management System for Redco Optimizer
 * 
 * Manages external API calls with rate limiting, caching, and error handling
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_API_Manager {
    
    /**
     * API endpoints
     */
    const ENDPOINT_PAGESPEED = 'https://www.googleapis.com/pagespeed/v5/runPagespeed';
    const ENDPOINT_WEBP_CHECK = 'https://developers.google.com/speed/webp/';
    
    /**
     * Rate limiting settings
     */
    const RATE_LIMIT_REQUESTS = 10;
    const RATE_LIMIT_WINDOW = 3600; // 1 hour
    
    /**
     * Cache durations
     */
    const CACHE_PAGESPEED = 3600; // 1 hour
    const CACHE_WEBP_CHECK = 86400; // 24 hours
    const CACHE_API_RESPONSE = 1800; // 30 minutes
    
    /**
     * Request timeout
     */
    const REQUEST_TIMEOUT = 30;
    
    /**
     * Initialize API manager
     */
    public static function init() {
        // Add admin action handlers
        add_action('admin_post_redco_clear_cache', array('Redco_Advanced_Cache', 'handle_admin_cache_clear'));
        
        // Add API health check
        add_action('redco_api_health_check', array(__CLASS__, 'health_check'));
        
        // Schedule API health check
        if (!wp_next_scheduled('redco_api_health_check')) {
            wp_schedule_event(time(), 'daily', 'redco_api_health_check');
        }
    }
    
    /**
     * Make API request with rate limiting and caching
     * 
     * @param string $endpoint API endpoint
     * @param array $params Request parameters
     * @param string $method HTTP method
     * @param int $cache_duration Cache duration in seconds
     * @return array|WP_Error API response or error
     */
    public static function make_request($endpoint, $params = array(), $method = 'GET', $cache_duration = null) {
        $cache_duration = $cache_duration ?: self::CACHE_API_RESPONSE;
        
        // Generate cache key
        $cache_key = 'api_' . md5($endpoint . serialize($params) . $method);
        
        // Try to get cached response
        $cached_response = Redco_Advanced_Cache::get($cache_key, Redco_Advanced_Cache::GROUP_API);
        if ($cached_response !== null) {
            return $cached_response;
        }
        
        // Check rate limiting
        if (!self::check_rate_limit($endpoint)) {
            $error = new WP_Error(
                'rate_limit_exceeded',
                'API rate limit exceeded for endpoint: ' . $endpoint
            );
            
            Redco_Error_Handler::warning(
                'API rate limit exceeded',
                Redco_Error_Handler::CONTEXT_API,
                array('endpoint' => $endpoint)
            );
            
            return $error;
        }
        
        // Start performance monitoring
        Redco_Performance_Monitor::start_timer('api_request_' . md5($endpoint));
        
        // Prepare request
        $url = $endpoint;
        $args = array(
            'timeout' => self::REQUEST_TIMEOUT,
            'method' => $method,
            'headers' => array(
                'User-Agent' => 'Redco Optimizer/' . REDCO_OPTIMIZER_VERSION,
                'Accept' => 'application/json'
            )
        );
        
        if ($method === 'GET' && !empty($params)) {
            $url = add_query_arg($params, $endpoint);
        } elseif ($method === 'POST') {
            $args['body'] = $params;
        }
        
        // Make request
        $response = wp_remote_request($url, $args);
        
        // End performance monitoring
        $execution_time = Redco_Performance_Monitor::end_timer('api_request_' . md5($endpoint));
        
        // Update rate limiting
        self::update_rate_limit($endpoint);
        
        // Handle response
        if (is_wp_error($response)) {
            // TEMPORARILY DISABLED: Error logging
            /*
            Redco_Error_Handler::error(
                'API request failed: ' . $response->get_error_message(),
                Redco_Error_Handler::CONTEXT_API,
                array(
                    'endpoint' => $endpoint,
                    'params' => $params,
                    'execution_time' => $execution_time
                )
            );
            */

            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            $error = new WP_Error(
                'api_error',
                'API returned error code: ' . $response_code,
                array('response_body' => $response_body)
            );
            
            // TEMPORARILY DISABLED: Error logging
            /*
            Redco_Error_Handler::error(
                'API error response',
                Redco_Error_Handler::CONTEXT_API,
                array(
                    'endpoint' => $endpoint,
                    'response_code' => $response_code,
                    'response_body' => substr($response_body, 0, 500)
                )
            );
            */
            
            return $error;
        }
        
        // Parse JSON response
        $data = json_decode($response_body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $error = new WP_Error(
                'json_parse_error',
                'Failed to parse JSON response: ' . json_last_error_msg()
            );
            
            // TEMPORARILY DISABLED: Error logging
            /*
            Redco_Error_Handler::error(
                'JSON parse error',
                Redco_Error_Handler::CONTEXT_API,
                array(
                    'endpoint' => $endpoint,
                    'json_error' => json_last_error_msg(),
                    'response_body' => substr($response_body, 0, 500)
                )
            );
            */
            
            return $error;
        }
        
        // Cache successful response
        Redco_Advanced_Cache::set($cache_key, $data, $cache_duration, Redco_Advanced_Cache::GROUP_API);
        
        Redco_Error_Handler::info(
            'API request successful',
            Redco_Error_Handler::CONTEXT_API,
            array(
                'endpoint' => $endpoint,
                'execution_time' => $execution_time,
                'response_size' => strlen($response_body)
            )
        );
        
        return $data;
    }
    
    /**
     * Get PageSpeed Insights data
     * 
     * @param string $url URL to analyze
     * @param string $api_key Google API key
     * @param string $strategy Strategy (mobile or desktop)
     * @return array|WP_Error PageSpeed data or error
     */
    public static function get_pagespeed_data($url, $api_key, $strategy = 'mobile') {
        if (empty($api_key)) {
            return new WP_Error('missing_api_key', 'PageSpeed API key is required');
        }
        
        $params = array(
            'url' => $url,
            'key' => $api_key,
            'strategy' => $strategy,
            'category' => 'performance'
        );
        
        $cache_key = 'pagespeed_' . md5($url . $strategy);
        
        return Redco_Advanced_Cache::remember(
            $cache_key,
            function() use ($params) {
                return self::make_request(self::ENDPOINT_PAGESPEED, $params, 'GET', self::CACHE_PAGESPEED);
            },
            self::CACHE_PAGESPEED,
            Redco_Advanced_Cache::GROUP_API
        );
    }
    
    /**
     * Check WebP browser support
     * 
     * @return array WebP support data
     */
    public static function check_webp_support() {
        return Redco_Advanced_Cache::remember(
            'webp_browser_support',
            function() {
                // Simulate WebP support check
                return array(
                    'chrome' => true,
                    'firefox' => true,
                    'safari' => true,
                    'edge' => true,
                    'opera' => true,
                    'support_percentage' => 95.2
                );
            },
            self::CACHE_WEBP_CHECK,
            Redco_Advanced_Cache::GROUP_API
        );
    }
    
    /**
     * Check rate limiting for endpoint
     * 
     * @param string $endpoint API endpoint
     * @return bool Whether request is allowed
     */
    private static function check_rate_limit($endpoint) {
        $rate_key = 'rate_limit_' . md5($endpoint);
        $current_requests = get_transient($rate_key) ?: 0;
        
        return $current_requests < self::RATE_LIMIT_REQUESTS;
    }
    
    /**
     * Update rate limiting counter
     * 
     * @param string $endpoint API endpoint
     */
    private static function update_rate_limit($endpoint) {
        $rate_key = 'rate_limit_' . md5($endpoint);
        $current_requests = get_transient($rate_key) ?: 0;
        
        set_transient($rate_key, $current_requests + 1, self::RATE_LIMIT_WINDOW);
    }
    
    /**
     * Get rate limiting status
     * 
     * @param string $endpoint API endpoint
     * @return array Rate limiting status
     */
    public static function get_rate_limit_status($endpoint) {
        $rate_key = 'rate_limit_' . md5($endpoint);
        $current_requests = get_transient($rate_key) ?: 0;
        $remaining = max(0, self::RATE_LIMIT_REQUESTS - $current_requests);
        
        return array(
            'requests_made' => $current_requests,
            'requests_remaining' => $remaining,
            'limit' => self::RATE_LIMIT_REQUESTS,
            'window_seconds' => self::RATE_LIMIT_WINDOW,
            'reset_time' => time() + self::RATE_LIMIT_WINDOW
        );
    }
    
    /**
     * Health check for API endpoints
     */
    public static function health_check() {
        $endpoints = array(
            'pagespeed' => self::ENDPOINT_PAGESPEED,
            'webp_check' => self::ENDPOINT_WEBP_CHECK
        );
        
        $health_status = array();
        
        foreach ($endpoints as $name => $endpoint) {
            $start_time = microtime(true);
            
            // Simple HEAD request to check availability
            $response = wp_remote_head($endpoint, array(
                'timeout' => 10,
                'headers' => array(
                    'User-Agent' => 'Redco Optimizer Health Check'
                )
            ));
            
            $response_time = microtime(true) - $start_time;
            $is_healthy = !is_wp_error($response) && wp_remote_retrieve_response_code($response) < 400;
            
            $health_status[$name] = array(
                'healthy' => $is_healthy,
                'response_time' => round($response_time * 1000, 2), // Convert to milliseconds
                'last_check' => current_time('mysql'),
                'endpoint' => $endpoint
            );
            
            if (!$is_healthy) {
                $error_message = is_wp_error($response) 
                    ? $response->get_error_message() 
                    : 'HTTP ' . wp_remote_retrieve_response_code($response);
                
                Redco_Error_Handler::warning(
                    "API endpoint unhealthy: {$name}",
                    Redco_Error_Handler::CONTEXT_API,
                    array(
                        'endpoint' => $endpoint,
                        'error' => $error_message,
                        'response_time' => $response_time
                    )
                );
            }
        }
        
        // Store health status
        update_option('redco_api_health_status', $health_status);
        
        Redco_Error_Handler::info(
            'API health check completed',
            Redco_Error_Handler::CONTEXT_API,
            array('endpoints_checked' => count($endpoints))
        );
    }
    
    /**
     * Get API health status
     * 
     * @return array Health status for all endpoints
     */
    public static function get_health_status() {
        return get_option('redco_api_health_status', array());
    }
    
    /**
     * Clear API cache
     */
    public static function clear_api_cache() {
        Redco_Advanced_Cache::clear_group(Redco_Advanced_Cache::GROUP_API);
        
        Redco_Error_Handler::info(
            'API cache cleared',
            Redco_Error_Handler::CONTEXT_API
        );
    }
    
    /**
     * Get API statistics
     * 
     * @return array API usage statistics
     */
    public static function get_api_stats() {
        $endpoints = array(
            self::ENDPOINT_PAGESPEED,
            self::ENDPOINT_WEBP_CHECK
        );
        
        $stats = array();
        
        foreach ($endpoints as $endpoint) {
            $rate_status = self::get_rate_limit_status($endpoint);
            $stats[md5($endpoint)] = array(
                'endpoint' => $endpoint,
                'requests_made' => $rate_status['requests_made'],
                'requests_remaining' => $rate_status['requests_remaining']
            );
        }
        
        return $stats;
    }
}
