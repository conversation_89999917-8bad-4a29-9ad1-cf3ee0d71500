<?php
/**
 * Standardized Module Sidebar Template
 * 
 * Provides consistent sidebar sections across all modules with:
 * - Module statistics display
 * - Performance impact information
 * - Technical details
 * - Quick actions panel
 * 
 * Variables expected:
 * - $module_key: Module identifier
 * - $stats: Array of statistics
 * - $is_enabled: Boolean module status
 * - $performance_impact: Array of performance benefits
 * - $technical_details: Array of technical information
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Default values
$module_key = $module_key ?? 'unknown';
$stats = $stats ?? array();
$is_enabled = $is_enabled ?? false;
$performance_impact = $performance_impact ?? array();
$technical_details = $technical_details ?? array();

// Default performance impact if not provided
if (empty($performance_impact)) {
    $performance_impact = array(
        __('Improved page load times', 'redco-optimizer'),
        __('Better user experience', 'redco-optimizer'),
        __('Enhanced Core Web Vitals', 'redco-optimizer'),
        __('Reduced server load', 'redco-optimizer')
    );
}
?>

<div class="redco-content-sidebar">
    
    <!-- Module Statistics -->
    <?php if ($is_enabled && !empty($stats)): ?>
    <div class="redco-sidebar-section">
        <div class="redco-sidebar-header">
            <h3>
                <span class="dashicons dashicons-chart-bar"></span>
                <?php _e('Module Statistics', 'redco-optimizer'); ?>
            </h3>
        </div>
        <div class="redco-sidebar-content">
            <div class="redco-stats-grid">
                <?php foreach ($stats as $key => $value): ?>
                    <div class="redco-stat-item">
                        <div class="redco-stat-icon">
                            <span class="dashicons <?php echo esc_attr(Redco_Sidebar_Helper::get_stat_icon($key)); ?>"></span>
                        </div>
                        <div class="redco-stat-value"><?php echo esc_html(Redco_Sidebar_Helper::format_stat_value($value)); ?></div>
                        <div class="redco-stat-label"><?php echo esc_html(Redco_Sidebar_Helper::format_stat_label($key)); ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Performance Impact -->
    <div class="redco-sidebar-section">
        <div class="redco-sidebar-header">
            <h3>
                <span class="dashicons dashicons-performance"></span>
                <?php _e('Performance Impact', 'redco-optimizer'); ?>
            </h3>
        </div>
        <div class="redco-sidebar-content">
            <?php if ($is_enabled): ?>
                <div class="performance-benefits">
                    <p><strong><?php _e('Active Benefits:', 'redco-optimizer'); ?></strong></p>
                    <ul class="benefits-list">
                        <?php foreach ($performance_impact as $benefit): ?>
                            <li>
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php echo esc_html($benefit); ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php else: ?>
                <div class="performance-potential">
                    <p><strong><?php _e('Potential Benefits:', 'redco-optimizer'); ?></strong></p>
                    <ul class="benefits-list">
                        <?php foreach ($performance_impact as $benefit): ?>
                            <li>
                                <span class="dashicons dashicons-clock"></span>
                                <?php echo esc_html($benefit); ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <p class="enable-note">
                        <span class="dashicons dashicons-info"></span>
                        <?php _e('Enable this module to unlock these benefits.', 'redco-optimizer'); ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="redco-sidebar-section">
        <div class="redco-sidebar-header">
            <h3>
                <span class="dashicons dashicons-admin-tools"></span>
                <?php _e('Quick Actions', 'redco-optimizer'); ?>
            </h3>
        </div>
        <div class="redco-sidebar-content">
            <div class="quick-actions-list">
                <?php if ($is_enabled): ?>
                    <button type="button" class="redco-btn redco-btn-primary sidebar-action-btn" data-action="optimize">
                        <span class="dashicons dashicons-performance"></span>
                        <?php _e('Optimize Settings', 'redco-optimizer'); ?>
                    </button>
                    
                    <button type="button" class="redco-btn redco-btn-secondary sidebar-action-btn" data-action="reset">
                        <span class="dashicons dashicons-undo"></span>
                        <?php _e('Reset to Defaults', 'redco-optimizer'); ?>
                    </button>
                    
                    <button type="button" class="redco-btn redco-btn-outline sidebar-action-btn" data-action="test">
                        <span class="dashicons dashicons-admin-tools"></span>
                        <?php _e('Test Configuration', 'redco-optimizer'); ?>
                    </button>
                <?php else: ?>
                    <button type="button" class="redco-btn redco-btn-primary sidebar-action-btn" data-action="enable">
                        <span class="dashicons dashicons-yes"></span>
                        <?php _e('Enable Module', 'redco-optimizer'); ?>
                    </button>
                    
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="redco-btn redco-btn-outline">
                        <span class="dashicons dashicons-admin-generic"></span>
                        <?php _e('Manage All Modules', 'redco-optimizer'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Technical Details -->
    <?php if (!empty($technical_details)): ?>
    <div class="redco-sidebar-section">
        <div class="redco-sidebar-header">
            <h3>
                <span class="dashicons dashicons-editor-code"></span>
                <?php _e('Technical Details', 'redco-optimizer'); ?>
            </h3>
        </div>
        <div class="redco-sidebar-content">
            <div class="technical-info">
                <?php foreach ($technical_details as $section => $details): ?>
                    <div class="tech-section">
                        <h4><?php echo esc_html($section); ?></h4>
                        <?php if (is_array($details)): ?>
                            <ul class="tech-list">
                                <?php foreach ($details as $detail): ?>
                                    <li><?php echo esc_html($detail); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p><?php echo esc_html($details); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Help & Support -->
    <div class="redco-sidebar-section">
        <div class="redco-sidebar-header">
            <h3>
                <span class="dashicons dashicons-sos"></span>
                <?php _e('Help & Support', 'redco-optimizer'); ?>
            </h3>
        </div>
        <div class="redco-sidebar-content">
            <div class="help-links">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="help-link">
                    <span class="dashicons dashicons-admin-tools"></span>
                    <?php _e('Run Diagnostics', 'redco-optimizer'); ?>
                </a>
                
                <a href="#" class="help-link" onclick="window.open('https://redconic.com/optimizer/docs', '_blank')">
                    <span class="dashicons dashicons-book"></span>
                    <?php _e('Documentation', 'redco-optimizer'); ?>
                </a>
                
                <a href="#" class="help-link" onclick="window.open('https://redconic.com/support', '_blank')">
                    <span class="dashicons dashicons-email"></span>
                    <?php _e('Contact Support', 'redco-optimizer'); ?>
                </a>
            </div>
        </div>
    </div>

</div>

<script>
jQuery(document).ready(function($) {
    // Handle sidebar action buttons
    $('.sidebar-action-btn').on('click', function(e) {
        e.preventDefault();
        
        const button = $(this);
        const action = button.data('action');
        const moduleKey = '<?php echo esc_js($module_key); ?>';
        
        // Prevent multiple clicks
        if (button.hasClass('loading')) {
            return;
        }
        
        const originalHtml = button.html();
        button.addClass('loading').html('<span class="dashicons dashicons-update-alt"></span> <?php _e("Processing...", "redco-optimizer"); ?>');
        
        // Handle different actions
        switch (action) {
            case 'optimize':
                handleOptimizeAction(moduleKey, button, originalHtml);
                break;
            case 'reset':
                handleResetAction(moduleKey, button, originalHtml);
                break;
            case 'test':
                handleTestAction(moduleKey, button, originalHtml);
                break;
            case 'enable':
                handleEnableAction(moduleKey, button, originalHtml);
                break;
        }
    });
    
    function handleOptimizeAction(moduleKey, button, originalHtml) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_apply_optimal_settings',
                module: moduleKey,
                nonce: '<?php echo wp_create_nonce("redco_optimizer_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showToast('<?php _e("Settings optimized successfully!", "redco-optimizer"); ?>', 'success');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(response.data || '<?php _e("Failed to optimize settings", "redco-optimizer"); ?>', 'error');
                }
            },
            error: function() {
                showToast('<?php _e("Network error occurred", "redco-optimizer"); ?>', 'error');
            },
            complete: function() {
                setTimeout(() => {
                    button.removeClass('loading').html(originalHtml);
                }, 1000);
            }
        });
    }
    
    function handleResetAction(moduleKey, button, originalHtml) {
        if (!confirm('<?php _e("Are you sure you want to reset all settings to defaults?", "redco-optimizer"); ?>')) {
            button.removeClass('loading').html(originalHtml);
            return;
        }
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_reset_module_settings',
                module: moduleKey,
                nonce: '<?php echo wp_create_nonce("redco_optimizer_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showToast('<?php _e("Settings reset to defaults", "redco-optimizer"); ?>', 'success');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(response.data || '<?php _e("Failed to reset settings", "redco-optimizer"); ?>', 'error');
                }
            },
            error: function() {
                showToast('<?php _e("Network error occurred", "redco-optimizer"); ?>', 'error');
            },
            complete: function() {
                setTimeout(() => {
                    button.removeClass('loading').html(originalHtml);
                }, 1000);
            }
        });
    }
    
    function handleTestAction(moduleKey, button, originalHtml) {
        // Redirect to diagnostic page with module focus
        window.location.href = '<?php echo admin_url("admin.php?page=redco-optimizer&tab=diagnostic-autofix&focus="); ?>' + moduleKey;
    }
    
    function handleEnableAction(moduleKey, button, originalHtml) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_toggle_module',
                module: moduleKey,
                enabled: true,
                nonce: '<?php echo wp_create_nonce("redco_optimizer_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showToast('<?php _e("Module enabled successfully!", "redco-optimizer"); ?>', 'success');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(response.data || '<?php _e("Failed to enable module", "redco-optimizer"); ?>', 'error');
                }
            },
            error: function() {
                showToast('<?php _e("Network error occurred", "redco-optimizer"); ?>', 'error');
            },
            complete: function() {
                setTimeout(() => {
                    button.removeClass('loading').html(originalHtml);
                }, 1000);
            }
        });
    }
    
    // Fallback toast function if not available globally
    function showToast(message, type) {
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }
});
</script>

<?php
// Helper methods for the template
class Redco_Sidebar_Helper {
    public static function get_stat_icon($stat_key) {
        $icons = array(
            'images_processed' => 'dashicons-format-image',
            'files_optimized' => 'dashicons-media-document',
            'bytes_saved' => 'dashicons-download',
            'cache_hits' => 'dashicons-yes-alt',
            'cache_size' => 'dashicons-database',
            'cached_pages' => 'dashicons-admin-page',
            'compression_ratio' => 'dashicons-performance',
            'threshold' => 'dashicons-visibility',
            'conversions' => 'dashicons-image-rotate',
            'quality' => 'dashicons-star-filled'
        );
        
        return $icons[$stat_key] ?? 'dashicons-chart-bar';
    }
    
    public static function format_stat_value($value) {
        if (is_numeric($value)) {
            if ($value > 1000000) {
                return round($value / 1000000, 1) . 'M';
            } elseif ($value > 1000) {
                return round($value / 1000, 1) . 'K';
            } else {
                return number_format($value);
            }
        }
        
        return $value;
    }
    
    public static function format_stat_label($key) {
        return ucwords(str_replace('_', ' ', $key));
    }
}

// Helper class is available as static methods
?>
