<?php
/**
 * CDN Integration Module Settings - Phase 4 Clean
 *
 * @package Redco_Optimizer
 * @subpackage CDN_Integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load Phase 4 CDN classes
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-cdn-provider-manager.php';

// Get module instance and settings
$is_enabled = redco_is_module_enabled('cdn-integration');
$active_provider = redco_get_module_option('cdn-integration', 'active_provider', '');
$cdn_enabled = redco_get_module_option('cdn-integration', 'cdn_enabled', false);
$current_settings = redco_get_module_option('cdn-integration', 'cdn_settings', array());

// Get all available providers
$providers = Redco_CDN_Provider_Manager::get_providers();

// Get provider status for each provider
$provider_statuses = array();
foreach ($providers as $provider_name => $provider_data) {
    $provider_statuses[$provider_name] = Redco_CDN_Provider_Manager::get_provider_status($provider_name);
}

// Get recommended provider
$recommended_provider = Redco_CDN_Provider_Manager::get_recommended_provider();

// Detect existing CDN
$existing_cdn = Redco_CDN_Provider_Manager::detect_existing_cdn();

// Get CDN statistics
$cdn_stats = array(
    'enabled' => $cdn_enabled && !empty($active_provider),
    'total_requests' => 0,
    'cache_hits' => 0,
    'bandwidth_saved' => 0
);

if ($is_enabled && !empty($active_provider)) {
    $provider_instance = Redco_CDN_Provider_Manager::get_provider_instance($active_provider);
    if ($provider_instance) {
        $stats = $provider_instance->get_cache_stats();
        $cdn_stats = array_merge($cdn_stats, $stats);
    }
}

// Calculate cache hit ratio for display
$stats = array(
    'cache_hit_ratio' => $cdn_stats['total_requests'] > 0 ?
        round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0
);
?>



<style>
/* Phase 4: CDN Provider Grid Styling */
.cdn-provider-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.cdn-provider-card {
    background: #fff;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.cdn-provider-card:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
    transform: translateY(-2px);
}

.cdn-provider-card.active {
    border-color: #4CAF50;
    background: #f8fff8;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}

.cdn-provider-card.recommended::before {
    content: "⭐ Recommended";
    position: absolute;
    top: -10px;
    right: 15px;
    background: #4CAF50;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.provider-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.provider-logo .provider-icon {
    width: 32px;
    height: 32px;
    object-fit: contain;
    border-radius: 4px;
    transition: transform 0.2s ease;
}

.provider-logo .provider-icon:hover {
    transform: scale(1.05);
}

.provider-info h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.provider-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.provider-status.status-configured {
    background: #d4edda;
    color: #155724;
}

.provider-status.status-not_configured {
    background: #fff3cd;
    color: #856404;
}

.provider-status.status-active {
    background: #d1ecf1;
    color: #0c5460;
}

.provider-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.provider-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    font-size: 13px;
    color: #555;
}

.provider-features .dashicons {
    color: #4CAF50;
    font-size: 14px;
}

.provider-metrics {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 10px 0;
    border-top: 1px solid #eee;
}

.metric {
    text-align: center;
    flex: 1;
}

.metric-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.metric-value.tier-enterprise {
    color: #8e44ad;
}

.metric-value.tier-high {
    color: #27ae60;
}

.metric-value.tier-security {
    color: #e74c3c;
}

.metric-label {
    display: block;
    font-size: 11px;
    color: #7f8c8d;
    text-transform: uppercase;
    margin-top: 2px;
}

.provider-actions {
    display: flex;
    gap: 10px;
}

.select-provider-btn,
.test-provider-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #4CAF50;
    background: #4CAF50;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.select-provider-btn:hover,
.test-provider-btn:hover {
    background: #45a049;
    border-color: #45a049;
}

.test-provider-btn {
    background: transparent;
    color: #4CAF50;
}

.test-provider-btn:hover {
    background: #4CAF50;
    color: white;
}

.existing-cdn-notice {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 12px 16px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #0066cc;
}

.existing-cdn-notice .dashicons {
    color: #0066cc;
}

/* Phase 4: Provider Configuration Card */
#provider-configuration-card {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* CDN Auto-Save Indicator */
.cdn-autosave-indicator {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 9999;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.cdn-autosave-indicator.saving {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.cdn-autosave-indicator.saving .dashicons {
    animation: spin 1s linear infinite;
}

.cdn-autosave-indicator.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.cdn-autosave-indicator.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Loading and error states for configuration */
.loading-state, .error-state {
    padding: 20px;
    text-align: center;
    border-radius: 4px;
    margin: 10px 0;
}

.loading-state {
    background: #f0f8ff;
    color: #0073aa;
    border: 1px solid #0073aa;
}

.loading-state .dashicons {
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

.error-state {
    background: #ffeaea;
    color: #dc3232;
    border: 1px solid #dc3232;
}

.error-state .dashicons {
    margin-right: 8px;
}

/* Enhanced button states */
.select-provider-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.enhanced-input.error {
    border-color: #dc3232 !important;
    box-shadow: 0 0 0 1px #dc3232 !important;
}

/* CDN Configuration Modal Styles */
.cdn-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cdn-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(2px);
}

.cdn-modal-container {
    position: relative;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.cdn-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.cdn-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cdn-modal-header .dashicons {
    color: #4CAF50;
    font-size: 20px;
}

.cdn-modal-close {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.cdn-modal-close:hover {
    background: #e9ecef;
}

.cdn-modal-close .dashicons {
    font-size: 18px;
    color: #6c757d;
}

.cdn-modal-content {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.cdn-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.cdn-modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.cdn-modal-btn-primary {
    background: #4CAF50;
    color: white;
}

.cdn-modal-btn-primary:hover {
    background: #45a049;
}

.cdn-modal-btn-secondary {
    background: #6c757d;
    color: white;
}

.cdn-modal-btn-secondary:hover {
    background: #5a6268;
}

.cdn-modal-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Prevent body scroll when modal is open */
body.cdn-modal-open {
    overflow: hidden;
}

/* Enhanced Optimization Tips Styles */
.tips-toggle {
    display: flex;
    align-items: center;
}

.tips-toggle-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.tips-toggle-btn:hover {
    background: #e9ecef;
    border-color: #ced4da;
}

.tips-toggle-btn .dashicons {
    font-size: 14px;
}

.tips-view {
    display: none;
}

.tips-view.active {
    display: block;
}

/* Quick Tips View */
.tip-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.tip-item:hover {
    background: #e9ecef;
    border-color: #4CAF50;
    transform: translateY(-1px);
}

.tip-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
}

.tip-icon {
    font-size: 16px;
    margin-right: 8px;
}

.tip-priority {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 2px 6px;
    border-radius: 3px;
    background: #6c757d;
    color: white;
}

.tip-item.priority-high .tip-priority {
    background: #dc3545;
}

.tip-item.priority-medium .tip-priority {
    background: #fd7e14;
}

.tip-item.priority-low .tip-priority {
    background: #28a745;
}

.tip-text {
    font-size: 13px;
    line-height: 1.4;
    color: #495057;
}

/* Detailed Tips View */
.tip-category {
    margin-bottom: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.category-header {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.category-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-header .dashicons {
    font-size: 16px;
}

.category-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.category-tips {
    padding: 16px;
    background: #fff;
}

.detailed-tip {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f1f3f4;
}

.detailed-tip:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.tip-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 13px;
    color: #2c3e50;
    margin-bottom: 8px;
}

.tip-content p {
    font-size: 12px;
    line-height: 1.5;
    color: #495057;
    margin: 0 0 8px 0;
}

.tip-metrics {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.metric {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 500;
    color: #6c757d;
}

/* Tips Filter */
.tips-filter {
    margin: 16px 0;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
}

.filter-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.filter-btn {
    background: #fff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.filter-btn.active {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

/* Tips Summary */
.tips-summary {
    margin-top: 16px;
    padding: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px;
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.summary-stats {
    display: flex;
    gap: 16px;
}

.summary-stats .stat-item {
    text-align: center;
}

.summary-stats .stat-number {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #4CAF50;
    line-height: 1;
}

.summary-stats .stat-label {
    font-size: 10px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 500;
}

/* Ensure auto-save indicator appears above WordPress admin bar */
@media screen and (max-width: 782px) {
    .cdn-autosave-indicator {
        top: 46px;
        right: 10px;
        font-size: 12px;
        padding: 6px 12px;
    }

    .cdn-modal-container {
        width: 95%;
        max-height: 90vh;
    }

    .cdn-modal-header,
    .cdn-modal-content,
    .cdn-modal-footer {
        padding: 16px;
    }
}
</style>

<div class="redco-module-tab" data-module="cdn-integration">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('CDN Integration', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-networking"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('CDN Integration', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Integrate with Content Delivery Networks to serve static assets from edge locations worldwide, reducing load times and server bandwidth.', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled && $cdn_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if (!empty($active_provider)): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-cloud"></span>
                                    <?php echo esc_html($providers[$active_provider]['name']); ?>
                                </div>
                            <?php endif; ?>
                        <?php elseif ($existing_cdn['detected']): ?>
                            <div class="status-badge warning">
                                <span class="dashicons dashicons-info"></span>
                                <?php printf(__('CDN Detected: %s', 'redco-optimizer'), ucfirst($existing_cdn['provider'])); ?>
                            </div>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="test-cdn-connection-header">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Test Connection', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="auto-configure-cdn">
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('Auto Configure', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="purge-cdn-cache-header">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Purge Cache', 'redco-optimizer'); ?>
                            </button>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo !empty($active_provider) ? esc_html($providers[$active_provider]['name']) : 'None'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Provider', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $cdn_stats['total_requests'] > 0 ? round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0; ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Hit Rate', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo size_format($cdn_stats['bandwidth_saved']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Saved', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="cdn-integration">
                    <?php wp_nonce_field('redco_cdn_integration_settings', 'redco_cdn_integration_nonce'); ?>

                    <!-- Enable CDN Integration -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('CDN Integration Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="setting-item enhanced">
                                <label class="setting-label checkbox-label">
                                    <input type="checkbox" name="cdn_enabled" value="1"
                                           <?php checked($cdn_enabled, 1); ?> class="enhanced-checkbox">
                                    <strong><?php _e('Enable CDN Integration', 'redco-optimizer'); ?></strong>
                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                </label>
                                <div class="setting-description">
                                    <p><?php _e('Enable CDN integration to serve static assets from your selected CDN provider for faster global content delivery.', 'redco-optimizer'); ?></p>
                                    <div class="setting-benefits">
                                        <span class="benefit-item">🌍 Global delivery</span>
                                        <span class="benefit-item">⚡ Faster loading</span>
                                        <span class="benefit-item">📉 Reduced bandwidth</span>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="active_provider" value="<?php echo esc_attr($active_provider); ?>" id="selected_provider_input">
                        </div>
                    </div>

                    <!-- Phase 4: CDN Provider Selection -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-cloud"></span>
                                <?php _e('CDN Provider Selection', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <?php if ($existing_cdn['detected']): ?>
                                <div class="existing-cdn-notice">
                                    <span class="dashicons dashicons-info"></span>
                                    <strong><?php _e('CDN Detected:', 'redco-optimizer'); ?></strong>
                                    <?php printf(__('We detected you\'re already using %s. You can configure it below for enhanced integration.', 'redco-optimizer'), ucfirst($existing_cdn['provider'])); ?>
                                </div>
                                <?php endif; ?>

                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="cdn-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <!-- CDN Provider Grid -->
                            <div class="cdn-provider-grid">
                                <?php foreach ($providers as $provider_name => $provider_data): ?>
                                <div class="cdn-provider-card <?php echo $active_provider === $provider_name ? 'active' : ''; ?> <?php echo $provider_name === $recommended_provider ? 'recommended' : ''; ?>" data-provider="<?php echo esc_attr($provider_name); ?>">
                                    <div class="provider-header">
                                        <div class="provider-logo">
                                            <?php
                                            $logo_file = isset($provider_data['logo']) ? $provider_data['logo'] : 'custom-cdn-logo.svg';
                                            $logo_path = REDCO_OPTIMIZER_PLUGIN_URL . 'assets/images/' . $logo_file;
                                            ?>
                                            <img src="<?php echo esc_url($logo_path); ?>" alt="<?php echo esc_attr($provider_data['name']); ?> Logo" class="provider-icon" />
                                        </div>
                                        <div class="provider-info">
                                            <h4><?php echo esc_html($provider_data['name']); ?></h4>
                                            <?php if ($provider_name === $recommended_provider): ?>
                                                <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                            <?php endif; ?>
                                            <div class="provider-status status-<?php echo esc_attr($provider_statuses[$provider_name]); ?>">
                                                <?php echo esc_html(ucfirst(str_replace('_', ' ', $provider_statuses[$provider_name]))); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="provider-features">
                                        <ul>
                                            <?php foreach (array_slice($provider_data['features'], 0, 3) as $feature): ?>
                                            <li><span class="dashicons dashicons-yes"></span> <?php echo esc_html($feature); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>

                                    <div class="provider-metrics">
                                        <div class="metric">
                                            <span class="metric-value"><?php echo esc_html($provider_data['edge_locations']); ?></span>
                                            <span class="metric-label"><?php _e('Edge Locations', 'redco-optimizer'); ?></span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-value tier-<?php echo esc_attr($provider_data['performance_tier']); ?>">
                                                <?php echo esc_html(ucfirst($provider_data['performance_tier'])); ?>
                                            </span>
                                            <span class="metric-label"><?php _e('Performance', 'redco-optimizer'); ?></span>
                                        </div>
                                    </div>

                                    <div class="provider-actions">
                                        <button type="button" class="select-provider-btn" data-provider="<?php echo esc_attr($provider_name); ?>">
                                            <?php echo $active_provider === $provider_name ? __('Configure', 'redco-optimizer') : __('Select', 'redco-optimizer'); ?>
                                        </button>
                                        <?php if ($provider_statuses[$provider_name] === 'configured'): ?>
                                        <button type="button" class="test-provider-btn" data-provider="<?php echo esc_attr($provider_name); ?>">
                                            <?php _e('Test', 'redco-optimizer'); ?>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>



                    <!-- File Types Configuration -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-media-document"></span>
                                <?php _e('File Types Configuration', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">


                            <div class="setting-item enhanced">
                                    <label class="setting-label">
                                        <strong><?php _e('File Types to Serve via CDN', 'redco-optimizer'); ?></strong>
                                        <span class="help-tooltip" title="<?php _e('Select which file types should be served through your CDN', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                    <div class="setting-control">
                                        <div class="checkbox-list">
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_images]" value="1"
                                                       <?php checked(isset($current_settings['enable_images']) ? $current_settings['enable_images'] : 1, 1); ?>
                                                       id="enable_images" class="enhanced-checkbox">
                                                <label for="enable_images"><?php _e('Images (JPG, PNG, GIF, WebP, SVG)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_css]" value="1"
                                                       <?php checked(isset($current_settings['enable_css']) ? $current_settings['enable_css'] : 1, 1); ?>
                                                       id="enable_css" class="enhanced-checkbox">
                                                <label for="enable_css"><?php _e('CSS Files', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_js]" value="1"
                                                       <?php checked(isset($current_settings['enable_js']) ? $current_settings['enable_js'] : 1, 1); ?>
                                                       id="enable_js" class="enhanced-checkbox">
                                                <label for="enable_js"><?php _e('JavaScript Files', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_fonts]" value="1"
                                                       <?php checked(isset($current_settings['enable_fonts']) ? $current_settings['enable_fonts'] : 1, 1); ?>
                                                       id="enable_fonts" class="enhanced-checkbox">
                                                <label for="enable_fonts"><?php _e('Font Files (WOFF, WOFF2, TTF)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_media]" value="1"
                                                       <?php checked(isset($current_settings['enable_media']) ? $current_settings['enable_media'] : 0, 1); ?>
                                                       id="enable_media" class="enhanced-checkbox">
                                                <label for="enable_media"><?php _e('Media Files (MP4, MP3, etc.)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_documents]" value="1"
                                                       <?php checked(isset($current_settings['enable_documents']) ? $current_settings['enable_documents'] : 0, 1); ?>
                                                       id="enable_documents" class="enhanced-checkbox">
                                                <label for="enable_documents"><?php _e('Documents (PDF, ZIP, etc.)', 'redco-optimizer'); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                <div class="setting-description">
                                    <p><?php _e('Select which file types should be served through your CDN. Images and CSS/JS files typically provide the best performance improvements.', 'redco-optimizer'); ?></p>
                                    <div class="setting-note">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Tip: Start with images and CSS/JS files for immediate performance benefits.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Advanced CDN Options', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">


                            <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="cdn_settings[skip_logged_in]" value="1"
                                               <?php checked(isset($current_settings['skip_logged_in']) ? $current_settings['skip_logged_in'] : 1, 1); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Skip CDN for Logged-in Users', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Disable CDN for logged-in users to avoid caching issues with dynamic content and admin functionality.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🔒 Prevents admin issues</span>
                                            <span class="benefit-item">🎯 Dynamic content support</span>
                                            <span class="benefit-item">✅ Better compatibility</span>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- CDN Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('CDN Performance', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-requests">
                                <span class="stat-value"><?php echo number_format($cdn_stats['total_requests']); ?></span>
                                <span class="stat-label"><?php _e('Total Requests', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-hits">
                                <span class="stat-value"><?php echo number_format($cdn_stats['cache_hits']); ?></span>
                                <span class="stat-label"><?php _e('Cache Hits', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-ratio">
                                <span class="stat-value"><?php echo $cdn_stats['total_requests'] > 0 ? round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0; ?>%</span>
                                <span class="stat-label"><?php _e('Hit Ratio', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-bandwidth">
                                <span class="stat-value"><?php echo size_format($cdn_stats['bandwidth_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bandwidth Saved', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CDN Management Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('CDN Management', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="sidebar-actions">
                            <div class="sidebar-action-item">
                                <button type="button" id="test-cdn-connection" class="sidebar-action-button">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Test Connection', 'redco-optimizer'); ?>
                                </button>

                            </div>

                            <div class="sidebar-action-item">
                                <button type="button" id="purge-cdn-cache" class="sidebar-action-button">
                                    <span class="dashicons dashicons-trash"></span>
                                    <?php _e('Purge Cache', 'redco-optimizer'); ?>
                                </button>

                            </div>
                        </div>

                        <div id="cdn-test-result" class="sidebar-test-result" style="display:none;"></div>
                        <div id="cdn-purge-result" class="sidebar-test-result" style="display:none;"></div>
                    </div>
                </div>

                <!-- CDN Provider Info -->
                <?php if (!empty($active_provider)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-cloud"></span>
                            <?php _e('Provider Info', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="provider-info">
                            <div class="provider-name">
                                <strong><?php echo esc_html($providers[$active_provider]['name']); ?></strong>
                            </div>
                            <div class="provider-status">
                                <?php if ($cdn_stats['enabled']): ?>
                                    <span class="status-badge enabled">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php _e('Connected', 'redco-optimizer'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge disabled">
                                        <span class="dashicons dashicons-warning"></span>
                                        <?php _e('Not Connected', 'redco-optimizer'); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Enhanced Optimization Tips -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e('CDN Optimization Guide', 'redco-optimizer'); ?>
                        </h3>
                        <div class="tips-toggle">
                            <button type="button" class="tips-toggle-btn" id="toggle-tips-view">
                                <span class="dashicons dashicons-list-view"></span>
                                <span class="toggle-text"><?php _e('Detailed View', 'redco-optimizer'); ?></span>
                            </button>
                        </div>
                    </div>
                    <div class="sidebar-section-content">
                        <!-- Quick Tips View (Default) -->
                        <div class="tips-view tips-quick active" id="tips-quick-view">
                            <div class="tips-list">
                                <div class="tip-item priority-high" data-category="setup">
                                    <div class="tip-header">
                                        <span class="tip-icon">🚀</span>
                                        <span class="tip-priority">High Priority</span>
                                    </div>
                                    <span class="tip-text"><?php _e('Start with images and CSS/JS files for immediate 40-60% performance boost.', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="tip-item priority-medium" data-category="monitoring">
                                    <div class="tip-header">
                                        <span class="tip-icon">📊</span>
                                        <span class="tip-priority">Monitor</span>
                                    </div>
                                    <span class="tip-text"><?php _e('Maintain 80%+ hit ratio for optimal performance. Check stats weekly.', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="tip-item priority-medium" data-category="maintenance">
                                    <div class="tip-header">
                                        <span class="tip-icon">🔄</span>
                                        <span class="tip-priority">Maintenance</span>
                                    </div>
                                    <span class="tip-text"><?php _e('Purge cache after major updates. Set up automated purging for efficiency.', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="tip-item priority-low" data-category="advanced">
                                    <div class="tip-header">
                                        <span class="tip-icon">⚡</span>
                                        <span class="tip-priority">Advanced</span>
                                    </div>
                                    <span class="tip-text"><?php _e('Use HTTP/2 push and preload hints for critical resources.', 'redco-optimizer'); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Tips View -->
                        <div class="tips-view tips-detailed" id="tips-detailed-view">
                            <div class="tips-categories">
                                <!-- Setup Category -->
                                <div class="tip-category" data-category="setup">
                                    <div class="category-header">
                                        <h4><span class="dashicons dashicons-admin-tools"></span> <?php _e('Initial Setup', 'redco-optimizer'); ?></h4>
                                        <span class="category-badge">4 tips</span>
                                    </div>
                                    <div class="category-tips">
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🎯</span>
                                                <?php _e('File Type Priority', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Enable CDN for images first (JPG, PNG, WebP) as they typically account for 60-70% of page weight. Then add CSS/JS files for additional 20-30% improvement.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">📈 Impact: High</span>
                                                    <span class="metric">⏱️ Setup: 5 min</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🌍</span>
                                                <?php _e('Geographic Benefits', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('CDN provides maximum benefit for users 500+ miles from your server. Use analytics to identify your audience location and choose CDN with strong presence in those regions.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">📊 Improvement: 200-500ms</span>
                                                    <span class="metric">🎯 Global reach</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🔒</span>
                                                <?php _e('Logged-in User Settings', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Keep "Skip CDN for logged-in users" enabled to prevent admin panel issues and ensure dynamic content works correctly for authenticated users.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">✅ Recommended</span>
                                                    <span class="metric">🛡️ Prevents issues</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">⚙️</span>
                                                <?php _e('Provider Selection', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Choose CDN provider based on your audience location, budget, and required features. Cloudflare offers best free tier, while premium providers offer advanced features.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">💰 Cost varies</span>
                                                    <span class="metric">🎯 Location matters</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Monitoring Category -->
                                <div class="tip-category" data-category="monitoring">
                                    <div class="category-header">
                                        <h4><span class="dashicons dashicons-chart-bar"></span> <?php _e('Performance Monitoring', 'redco-optimizer'); ?></h4>
                                        <span class="category-badge">3 tips</span>
                                    </div>
                                    <div class="category-tips">
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">📊</span>
                                                <?php _e('Hit Ratio Optimization', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Monitor CDN hit ratio weekly. 80%+ is excellent, 60-80% is good, below 60% needs investigation. Low ratios often indicate caching configuration issues.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">🎯 Target: 80%+</span>
                                                    <span class="metric">📅 Check weekly</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">⚡</span>
                                                <?php _e('Core Web Vitals Impact', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('CDN directly improves Largest Contentful Paint (LCP) and Cumulative Layout Shift (CLS). Monitor these metrics in Google PageSpeed Insights after CDN implementation.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">📈 LCP improvement</span>
                                                    <span class="metric">🎯 SEO boost</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🔍</span>
                                                <?php _e('Bandwidth Analysis', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Track bandwidth savings to measure CDN ROI. Typical savings range from 40-70% of origin server bandwidth, reducing hosting costs and improving server performance.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">💰 Cost savings</span>
                                                    <span class="metric">📉 40-70% reduction</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Maintenance Category -->
                                <div class="tip-category" data-category="maintenance">
                                    <div class="category-header">
                                        <h4><span class="dashicons dashicons-update"></span> <?php _e('Maintenance & Updates', 'redco-optimizer'); ?></h4>
                                        <span class="category-badge">2 tips</span>
                                    </div>
                                    <div class="category-tips">
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🔄</span>
                                                <?php _e('Cache Purging Strategy', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Purge CDN cache after theme updates, plugin changes, or content modifications. Set up automated purging for WordPress updates to ensure users see fresh content immediately.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">🤖 Automate</span>
                                                    <span class="metric">⚡ Immediate updates</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🧪</span>
                                                <?php _e('Testing & Validation', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Test CDN functionality monthly using the built-in connection test. Verify that critical resources load correctly and check for any SSL certificate issues.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">🔍 Monthly check</span>
                                                    <span class="metric">🛡️ SSL validation</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced Category -->
                                <div class="tip-category" data-category="advanced">
                                    <div class="category-header">
                                        <h4><span class="dashicons dashicons-performance"></span> <?php _e('Advanced Optimization', 'redco-optimizer'); ?></h4>
                                        <span class="category-badge">3 tips</span>
                                    </div>
                                    <div class="category-tips">
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🚀</span>
                                                <?php _e('HTTP/2 & Preloading', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Enable HTTP/2 push for critical CSS and implement resource preloading for above-the-fold images. This can reduce initial page load time by 15-25%.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">⚡ 15-25% faster</span>
                                                    <span class="metric">🎯 Critical resources</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🖼️</span>
                                                <?php _e('Image Optimization', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Combine CDN with WebP conversion and responsive images. Use CDN\'s image optimization features if available for automatic format conversion and compression.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">📉 50-80% smaller</span>
                                                    <span class="metric">🎨 Auto-optimization</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detailed-tip">
                                            <div class="tip-title">
                                                <span class="tip-icon">🔧</span>
                                                <?php _e('Edge Computing', 'redco-optimizer'); ?>
                                            </div>
                                            <div class="tip-content">
                                                <p><?php _e('Utilize CDN edge computing features for dynamic content caching, API acceleration, and serverless functions to reduce origin server load.', 'redco-optimizer'); ?></p>
                                                <div class="tip-metrics">
                                                    <span class="metric">🌐 Edge processing</span>
                                                    <span class="metric">📈 Scalability</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tips Filter -->
                        <div class="tips-filter">
                            <div class="filter-buttons">
                                <button type="button" class="filter-btn active" data-filter="all"><?php _e('All Tips', 'redco-optimizer'); ?></button>
                                <button type="button" class="filter-btn" data-filter="setup"><?php _e('Setup', 'redco-optimizer'); ?></button>
                                <button type="button" class="filter-btn" data-filter="monitoring"><?php _e('Monitor', 'redco-optimizer'); ?></button>
                                <button type="button" class="filter-btn" data-filter="maintenance"><?php _e('Maintain', 'redco-optimizer'); ?></button>
                                <button type="button" class="filter-btn" data-filter="advanced"><?php _e('Advanced', 'redco-optimizer'); ?></button>
                            </div>
                        </div>

                        <!-- Tips Summary -->
                        <div class="tips-summary">
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <span class="stat-number">12</span>
                                    <span class="stat-label"><?php _e('Total Tips', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">4</span>
                                    <span class="stat-label"><?php _e('Categories', 'redco-optimizer'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
        <div class="module-disabled-notice">
            <div class="redco-notice warning">
                <p><?php _e('This module is currently disabled. Enable it to access CDN integration settings.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- CDN Provider Configuration Modal -->
<div id="cdn-config-modal" class="cdn-modal" style="display: none;">
    <div class="cdn-modal-overlay"></div>
    <div class="cdn-modal-container">
        <div class="cdn-modal-header">
            <h3 id="cdn-modal-title">
                <span class="dashicons dashicons-admin-settings"></span>
                <span id="cdn-modal-provider-name"><?php _e('Provider Configuration', 'redco-optimizer'); ?></span>
            </h3>
            <button type="button" class="cdn-modal-close" aria-label="<?php _e('Close', 'redco-optimizer'); ?>">
                <span class="dashicons dashicons-no-alt"></span>
            </button>
        </div>
        <div class="cdn-modal-content">
            <div id="cdn-modal-config-content">
                <!-- Dynamic provider configuration will be loaded here -->
            </div>
        </div>
        <div class="cdn-modal-footer">
            <button type="button" class="cdn-modal-btn cdn-modal-btn-secondary" id="cdn-modal-cancel">
                <?php _e('Cancel', 'redco-optimizer'); ?>
            </button>
            <button type="button" class="cdn-modal-btn cdn-modal-btn-primary" id="cdn-modal-save">
                <?php _e('Save Configuration', 'redco-optimizer'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    console.log('CDN Integration JavaScript loaded');

    // Phase 4: CDN Provider Selection - Enhanced with comprehensive error handling
    $('.select-provider-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $button = $(this);
        var provider = $button.data('provider');
        var $card = $button.closest('.cdn-provider-card');
        var providerName = $card.find('h4').text();

        console.log('CDN Provider Selection - Button clicked:', {
            provider: provider,
            providerName: providerName,
            buttonElement: $button[0],
            cardElement: $card[0]
        });

        // Validate provider data
        if (!provider) {
            console.error('CDN Provider Selection - No provider data found on button');
            showCdnAutoSaveStatus('error', 'Error: No provider data found');
            return;
        }

        if (!providerName) {
            console.error('CDN Provider Selection - No provider name found in card');
            showCdnAutoSaveStatus('error', 'Error: Provider name not found');
            return;
        }

        // Disable button temporarily to prevent double-clicks
        $button.prop('disabled', true);

        try {
            // Update active provider visual state
            $('.cdn-provider-card').removeClass('active');
            $card.addClass('active');

            // Update button text based on selection
            $('.select-provider-btn').each(function() {
                var $btn = $(this);
                var btnProvider = $btn.data('provider');
                if (btnProvider === provider) {
                    $btn.text('Configure');
                } else {
                    $btn.text('Select');
                }
            });

            // Update hidden input for form submission
            var $hiddenInput = $('#selected_provider_input');
            if ($hiddenInput.length) {
                $hiddenInput.val(provider);
                console.log('CDN Provider Selection - Hidden input updated:', $hiddenInput.val());
            } else {
                console.error('CDN Provider Selection - Hidden input not found');
            }

            // Open configuration modal for the selected provider
            openConfigurationModal(provider, providerName);

            // Show success status
            showCdnAutoSaveStatus('success', 'Provider selected: ' + providerName);

            // Trigger auto-save after a short delay
            setTimeout(function() {
                triggerCdnAutoSave();
            }, 300);

        } catch (error) {
            console.error('CDN Provider Selection - Error during selection:', error);
            showCdnAutoSaveStatus('error', 'Error selecting provider: ' + error.message);
        } finally {
            // Re-enable button
            setTimeout(function() {
                $button.prop('disabled', false);
            }, 500);
        }
    });

    // Open configuration modal for provider
    function openConfigurationModal(provider, providerName) {
        console.log('CDN Modal - Opening configuration modal for:', provider, providerName);

        var $modal = $('#cdn-config-modal');
        var $modalContent = $('#cdn-modal-config-content');
        var $modalTitle = $('#cdn-modal-provider-name');

        if (!$modal.length || !$modalContent.length) {
            console.error('CDN Modal - Modal elements not found');
            showCdnAutoSaveStatus('error', 'Modal not found');
            return;
        }

        try {
            // Set modal title
            $modalTitle.text(providerName + ' Configuration');

            // Show loading state in modal
            $modalContent.html('<div class="loading-state"><span class="dashicons dashicons-update"></span> Loading configuration...</div>');

            // Show modal with animation
            $modal.fadeIn(300);
            $('body').addClass('cdn-modal-open');

            // Load configuration content
            setTimeout(function() {
                loadModalConfiguration(provider, providerName, $modalContent);
            }, 200);

        } catch (error) {
            console.error('CDN Modal - Error opening modal:', error);
            showCdnAutoSaveStatus('error', 'Failed to open configuration modal');
        }
    }

    // Load provider configuration in modal
    function loadModalConfiguration(provider, providerName, $content) {
        console.log('CDN Modal - Loading configuration for:', provider, providerName);

        try {
            // Create basic configuration form based on provider
            var configHtml = '<div class="provider-config-form">';
            configHtml += '<div class="settings-intro">';

            configHtml += '</div>';

            // Add provider-specific fields
            switch(provider) {
            case 'cloudflare':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Zone ID</strong> <span class="help-tooltip" title="Your Cloudflare Zone ID">?</span></label>';
                configHtml += '<div class="setting-control"><input type="text" name="cdn_settings[cloudflare_zone_id]" class="enhanced-input" placeholder="Your Zone ID" data-setting="cloudflare_zone_id"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Token</strong> <span class="help-tooltip" title="Your Cloudflare API Token">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[cloudflare_api_token]" class="enhanced-input" placeholder="Your API Token" data-setting="cloudflare_api_token"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Zone URL</strong> <span class="help-tooltip" title="Your Cloudflare Zone URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[cloudflare_zone_url]" class="enhanced-input" placeholder="https://your-domain.com" data-setting="cloudflare_zone_url"></div>';
                configHtml += '</div>';
                break;
            case 'bunnycdn':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Key</strong> <span class="help-tooltip" title="Your BunnyCDN API Key">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[bunnycdn_api_key]" class="enhanced-input" placeholder="Your API Key" data-setting="bunnycdn_api_key"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Pull Zone URL</strong> <span class="help-tooltip" title="Your BunnyCDN Pull Zone URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[bunnycdn_pull_zone_url]" class="enhanced-input" placeholder="https://your-zone.b-cdn.net" data-setting="bunnycdn_pull_zone_url"></div>';
                configHtml += '</div>';
                break;
            case 'keycdn':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Key</strong> <span class="help-tooltip" title="Your KeyCDN API Key">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[keycdn_api_key]" class="enhanced-input" placeholder="Your API Key" data-setting="keycdn_api_key"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Zone URL</strong> <span class="help-tooltip" title="Your KeyCDN Zone URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[keycdn_zone_url]" class="enhanced-input" placeholder="https://your-zone.kxcdn.com" data-setting="keycdn_zone_url"></div>';
                configHtml += '</div>';
                break;
            case 'amazon_cloudfront':
            case 'cloudfront':
                configHtml += '<div class="setting-note"><span class="dashicons dashicons-info"></span> Note: Full CloudFront integration requires AWS SDK. Basic configuration is available.</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Distribution ID</strong> <span class="help-tooltip" title="Your CloudFront Distribution ID">?</span></label>';
                configHtml += '<div class="setting-control"><input type="text" name="cdn_settings[cloudfront_distribution_id]" class="enhanced-input" placeholder="E1234567890123" data-setting="cloudfront_distribution_id"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Distribution URL</strong> <span class="help-tooltip" title="Your CloudFront Distribution URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[cloudfront_distribution_url]" class="enhanced-input" placeholder="https://d1234567890123.cloudfront.net" data-setting="cloudfront_distribution_url"></div>';
                configHtml += '</div>';
                break;
            case 'sucuri':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Key</strong> <span class="help-tooltip" title="Your Sucuri API Key">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[sucuri_api_key]" class="enhanced-input" placeholder="Your API Key" data-setting="sucuri_api_key"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>CDN URL</strong> <span class="help-tooltip" title="Your Sucuri CDN URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[sucuri_cdn_url]" class="enhanced-input" placeholder="https://your-domain.sucuri.net" data-setting="sucuri_cdn_url"></div>';
                configHtml += '</div>';
                break;
            case 'fastly':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Token</strong> <span class="help-tooltip" title="Your Fastly API Token">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[fastly_api_token]" class="enhanced-input" placeholder="Your API Token" data-setting="fastly_api_token"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Service ID</strong> <span class="help-tooltip" title="Your Fastly Service ID">?</span></label>';
                configHtml += '<div class="setting-control"><input type="text" name="cdn_settings[fastly_service_id]" class="enhanced-input" placeholder="1234567890abcdef" data-setting="fastly_service_id"></div>';
                configHtml += '</div>';
                break;
            case 'custom':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>CDN URL</strong> <span class="help-tooltip" title="Your custom CDN URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[custom_cdn_url]" class="enhanced-input" placeholder="https://cdn.your-domain.com" data-setting="custom_cdn_url"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-note"><span class="dashicons dashicons-info"></span> Note: Cache purging and advanced features may not be available with custom CDN configurations.</div>';
                break;
            default:
                configHtml += '<div class="setting-note"><span class="dashicons dashicons-info"></span> Configuration options will be available once you select a provider.</div>';
                console.warn('CDN Configuration - Unknown provider:', provider);
        }

        configHtml += '</div>';

        // Update modal content
        setTimeout(function() {
            $content.html(configHtml);

            // Populate saved values from existing form fields
            populateModalSavedValues($content, provider);

            // Store current provider for modal operations
            $content.data('current-provider', provider);

            console.log('CDN Modal - Configuration loaded successfully for:', provider);
        }, 300);

        } catch (error) {
            console.error('CDN Modal - Error loading configuration:', error);
            $content.html('<div class="error-state"><span class="dashicons dashicons-warning"></span> Error loading configuration: ' + error.message + '</div>');
        }
    }

    // Close configuration modal
    function closeConfigurationModal() {
        var $modal = $('#cdn-config-modal');
        $modal.fadeOut(300);
        $('body').removeClass('cdn-modal-open');
        console.log('CDN Modal - Configuration modal closed');
    }

    // Save modal configuration
    function saveModalConfiguration() {
        var $modal = $('#cdn-config-modal');
        var $content = $('#cdn-modal-config-content');
        var $saveBtn = $('#cdn-modal-save');
        var provider = $content.data('current-provider');

        if (!provider) {
            console.error('CDN Modal - No provider data found for saving');
            return;
        }

        console.log('CDN Modal - Saving configuration for provider:', provider);

        // Disable save button
        $saveBtn.prop('disabled', true).text('Saving...');

        // Collect form data from modal
        var settings = {};
        $content.find('input[data-setting]').each(function() {
            var $field = $(this);
            var settingName = $field.data('setting');
            var value = $field.val().trim();

            if (value) {
                settings[settingName] = value;
            }
        });

        // Add provider to settings
        settings['active_provider'] = provider;

        console.log('CDN Modal - Collected settings:', settings);

        // Save via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_save_cdn_settings',
                settings: settings,
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                console.log('CDN Modal - Save response:', response);
                if (response.success) {
                    showCdnAutoSaveStatus('success', 'Configuration saved successfully');

                    // Update main form with new values
                    updateMainFormValues(settings);

                    // Close modal after short delay
                    setTimeout(function() {
                        closeConfigurationModal();
                    }, 1000);
                } else {
                    showCdnAutoSaveStatus('error', response.data.message || 'Failed to save configuration');
                }
            },
            error: function(xhr, status, error) {
                console.error('CDN Modal - Save error:', error);
                showCdnAutoSaveStatus('error', 'Network error: ' + error);
            },
            complete: function() {
                $saveBtn.prop('disabled', false).text('Save Configuration');
            }
        });
    }

    // Function to populate saved values in modal configuration fields
    function populateModalSavedValues($content, provider) {
        console.log('CDN Modal - Populating saved values for provider:', provider);

        // Get current settings from PHP
        <?php if (!empty($current_settings)): ?>
        var phpSettings = <?php echo json_encode($current_settings); ?>;
        console.log('CDN Modal - PHP settings available:', phpSettings);

        $content.find('input[data-setting]').each(function() {
            var $field = $(this);
            var settingName = $field.data('setting');

            if (phpSettings[settingName]) {
                $field.val(phpSettings[settingName]);
                console.log('CDN Modal - Populated from PHP:', settingName, phpSettings[settingName]);
            }
        });
        <?php endif; ?>
    }

    // Update main form values after modal save
    function updateMainFormValues(settings) {
        console.log('CDN Modal - Updating main form values:', settings);

        var $form = $('.redco-module-form[data-module="cdn-integration"]');

        // Update hidden input for active provider
        if (settings.active_provider) {
            $('#selected_provider_input').val(settings.active_provider);
        }

        // Update any existing form fields
        $.each(settings, function(key, value) {
            if (key !== 'active_provider') {
                var $field = $form.find('input[name="cdn_settings[' + key + ']"]');
                if ($field.length) {
                    $field.val(value);
                }
            }
        });
    }

    // Modal event handlers
    $('#cdn-modal-close, #cdn-modal-cancel, .cdn-modal-overlay').on('click', function(e) {
        e.preventDefault();
        closeConfigurationModal();
    });

    $('#cdn-modal-save').on('click', function(e) {
        e.preventDefault();
        saveModalConfiguration();
    });

    // Close modal on Escape key
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27 && $('#cdn-config-modal').is(':visible')) {
            closeConfigurationModal();
        }
    });

    // Prevent modal close when clicking inside modal container
    $('.cdn-modal-container').on('click', function(e) {
        e.stopPropagation();
    });

    // Test CDN connection (both header and sidebar buttons)
    $('#test-cdn-connection, #test-cdn-connection-header').on('click', function() {
        var $button = $(this);
        var $result = $('#cdn-test-result');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Testing...');
        $result.hide().removeClass('success error');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_test_cdn_connection',
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $result.addClass('success')
                           .html('<strong>Success:</strong> ' + response.data.message).show();
                } else {
                    $result.addClass('error')
                           .html('<strong>Error:</strong> ' + response.data.message).show();
                }
            },
            error: function() {
                $result.addClass('error')
                       .html('<strong>Error:</strong> Failed to test CDN connection').show();
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // Purge CDN cache (both header and sidebar buttons)
    $('#purge-cdn-cache, #purge-cdn-cache-header').on('click', function() {
        var $button = $(this);
        var $result = $('#cdn-purge-result');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Purging...');
        $result.hide().removeClass('success error');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_purge_cdn_cache',
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $result.addClass('success')
                           .html('<strong>Success:</strong> ' + response.data.message).show();
                } else {
                    $result.addClass('error')
                           .html('<strong>Error:</strong> ' + response.data.message).show();
                }
            },
            error: function() {
                $result.addClass('error')
                       .html('<strong>Error:</strong> Failed to purge CDN cache').show();
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // CDN Auto-Save Functionality
    var cdnAutoSaveTimer = null;
    var cdnAutoSaveInProgress = false;
    var cdnLastSavedData = null;

    // Auto-save status indicator
    function showCdnAutoSaveStatus(status, message) {
        var $indicator = $('#cdn-autosave-indicator');
        if (!$indicator.length) {
            $indicator = $('<div id="cdn-autosave-indicator" class="cdn-autosave-indicator"></div>');
            $('.redco-module-form[data-module="cdn-integration"]').prepend($indicator);
        }

        $indicator.removeClass('saving success error').addClass(status);
        $indicator.html('<span class="dashicons dashicons-' +
            (status === 'saving' ? 'update' : status === 'success' ? 'yes-alt' : 'warning') +
            '"></span>' + message);

        $indicator.show();

        if (status === 'success' || status === 'error') {
            setTimeout(function() {
                $indicator.fadeOut();
            }, status === 'success' ? 2000 : 5000);
        }
    }

    // Debounced auto-save function - Enhanced with validation
    function triggerCdnAutoSave() {
        console.log('CDN Auto-Save - Trigger called, in progress:', cdnAutoSaveInProgress);

        if (cdnAutoSaveInProgress) {
            console.log('CDN Auto-Save - Already in progress, skipping');
            return;
        }

        clearTimeout(cdnAutoSaveTimer);
        cdnAutoSaveTimer = setTimeout(function() {
            console.log('CDN Auto-Save - Executing auto-save after delay');
            performCdnAutoSave();
        }, 2500); // 2.5 second delay

        console.log('CDN Auto-Save - Timer set for 2.5 seconds');
    }

    // Perform auto-save - Enhanced with comprehensive validation
    function performCdnAutoSave() {
        console.log('CDN Auto-Save - Starting auto-save process');

        if (cdnAutoSaveInProgress) {
            console.log('CDN Auto-Save - Already in progress, aborting');
            return;
        }

        var $form = $('.redco-module-form[data-module="cdn-integration"]');
        if (!$form.length) {
            console.error('CDN Auto-Save - Form not found');
            showCdnAutoSaveStatus('error', 'Form not found');
            return;
        }

        console.log('CDN Auto-Save - Form found, serializing data');
        var formData = $form.serializeArray();
        var settings = {};

        // Convert form data to object - Phase 4 format
        $.each(formData, function(i, field) {
            if (field.name.startsWith('cdn_settings[') && field.name.endsWith(']')) {
                var fieldName = field.name.replace('cdn_settings[', '').replace(']', '');
                settings[fieldName] = field.value;
            } else if (field.name === 'cdn_enabled' || field.name === 'active_provider') {
                settings[field.name] = field.value;
            }
        });

        console.log('CDN Auto-Save - Form data converted:', settings);

        // Handle checkboxes explicitly - Phase 4 format
        $form.find('input[type="checkbox"]').each(function() {
            var $checkbox = $(this);
            var name = $checkbox.attr('name');
            if (name && name.startsWith('cdn_settings[')) {
                var fieldName = name.replace('cdn_settings[', '').replace(']', '');
                settings[fieldName] = $checkbox.is(':checked') ? 1 : 0;
            } else if (name === 'cdn_enabled') {
                settings[name] = $checkbox.is(':checked') ? 1 : 0;
            }
        });

        // Check if data has changed
        var currentDataString = JSON.stringify(settings);
        console.log('CDN Auto-Save - Comparing data:', {
            current: currentDataString,
            last: cdnLastSavedData,
            changed: currentDataString !== cdnLastSavedData
        });

        if (currentDataString === cdnLastSavedData) {
            console.log('CDN Auto-Save - No changes detected, skipping save');
            return;
        }

        cdnAutoSaveInProgress = true;
        showCdnAutoSaveStatus('saving', 'Saving CDN settings...');
        console.log('CDN Auto-Save - Starting AJAX request');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_save_cdn_settings',
                settings: settings,
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            timeout: 30000, // 30 second timeout
            success: function(response) {
                console.log('CDN Auto-Save - AJAX success:', response);
                if (response.success) {
                    cdnLastSavedData = currentDataString;
                    showCdnAutoSaveStatus('success', 'CDN settings saved');
                    console.log('CDN Auto-Save - Settings saved successfully');
                } else {
                    var errorMsg = response.data && response.data.message ? response.data.message : 'Error saving settings';
                    showCdnAutoSaveStatus('error', errorMsg);
                    console.error('CDN Auto-Save - Server error:', errorMsg);
                }
            },
            error: function(xhr, status, error) {
                console.error('CDN Auto-Save - AJAX error:', {
                    status: xhr.status,
                    statusText: status,
                    error: error,
                    responseText: xhr.responseText
                });

                var errorMessage;
                if (status === 'timeout') {
                    errorMessage = 'Request timeout - Please try again';
                } else if (xhr.status === 500) {
                    errorMessage = 'Server error - Check error logs';
                } else if (xhr.status === 403) {
                    errorMessage = 'Permission denied - Please refresh the page';
                } else {
                    errorMessage = 'Network error: ' + error;
                }
                showCdnAutoSaveStatus('error', errorMessage);
            },
            complete: function() {
                cdnAutoSaveInProgress = false;
                console.log('CDN Auto-Save - AJAX request completed');
            }
        });
    }

    // Bind auto-save to form changes
    $('.redco-module-form[data-module="cdn-integration"]').on('input change', 'input, select, textarea', function() {
        triggerCdnAutoSave();
    });

    // Special handling for provider selection to trigger auto-save
    $('.select-provider-btn').on('click', function() {
        setTimeout(function() {
            triggerCdnAutoSave();
        }, 100); // Small delay to allow configuration loading
    });

    // Initialize auto-save with current form state
    function initializeCdnAutoSave() {
        var $form = $('.redco-module-form[data-module="cdn-integration"]');
        if ($form.length) {
            var formData = $form.serializeArray();
            var settings = {};

            // Convert form data to object - Phase 4 format
            $.each(formData, function(i, field) {
                if (field.name.startsWith('cdn_settings[') && field.name.endsWith(']')) {
                    var fieldName = field.name.replace('cdn_settings[', '').replace(']', '');
                    settings[fieldName] = field.value;
                } else if (field.name === 'cdn_enabled' || field.name === 'active_provider') {
                    settings[field.name] = field.value;
                }
            });

            // Handle checkboxes explicitly - Phase 4 format
            $form.find('input[type="checkbox"]').each(function() {
                var $checkbox = $(this);
                var name = $checkbox.attr('name');
                if (name && name.startsWith('cdn_settings[')) {
                    var fieldName = name.replace('cdn_settings[', '').replace(']', '');
                    settings[fieldName] = $checkbox.is(':checked') ? 1 : 0;
                } else if (name === 'cdn_enabled') {
                    settings[name] = $checkbox.is(':checked') ? 1 : 0;
                }
            });

            // Store initial state
            cdnLastSavedData = JSON.stringify(settings);
        }
    }

    // Initialize auto-save when page loads
    initializeCdnAutoSave();

    // Performance impact indicator for Phase 4
    $('.select-provider-btn').on('click', function() {
        var $impact = $('#cdn-impact');
        $impact.removeClass('low medium').addClass('high').text('High Performance');
    });

    // Comprehensive initialization check
    function validateCdnIntegrationSetup() {
        console.log('CDN Integration - Running setup validation');

        var issues = [];

        // Check for required elements
        if (!$('.select-provider-btn').length) {
            issues.push('Select provider buttons not found');
        }

        if (!$('#selected_provider_input').length) {
            issues.push('Hidden provider input not found');
        }

        if (!$('#cdn-config-modal').length) {
            issues.push('Configuration modal not found');
        }

        if (!$('.redco-module-form[data-module="cdn-integration"]').length) {
            issues.push('CDN form not found');
        }

        if (typeof ajaxurl === 'undefined') {
            issues.push('AJAX URL not defined');
        }

        if (issues.length > 0) {
            console.error('CDN Integration - Setup issues found:', issues);
            showCdnAutoSaveStatus('error', 'Setup issues detected: ' + issues.join(', '));
        } else {
            console.log('CDN Integration - Setup validation passed');
        }

        return issues.length === 0;
    }

    // Run validation after DOM is ready
    setTimeout(validateCdnIntegrationSetup, 100);



    // Add click event debugging
    $(document).on('click', '.select-provider-btn', function(e) {
        console.log('CDN Integration - Direct click event captured:', {
            target: e.target,
            currentTarget: e.currentTarget,
            provider: $(this).data('provider')
        });
    });

    // Enhanced Optimization Tips Functionality

    // Toggle between quick and detailed view
    $('#toggle-tips-view').on('click', function() {
        var $button = $(this);
        var $quickView = $('#tips-quick-view');
        var $detailedView = $('#tips-detailed-view');
        var $toggleText = $button.find('.toggle-text');
        var $icon = $button.find('.dashicons');

        if ($quickView.hasClass('active')) {
            // Switch to detailed view
            $quickView.removeClass('active');
            $detailedView.addClass('active');
            $toggleText.text('Quick View');
            $icon.removeClass('dashicons-list-view').addClass('dashicons-grid-view');
            $button.attr('title', 'Switch to quick tips view');
        } else {
            // Switch to quick view
            $detailedView.removeClass('active');
            $quickView.addClass('active');
            $toggleText.text('Detailed View');
            $icon.removeClass('dashicons-grid-view').addClass('dashicons-list-view');
            $button.attr('title', 'Switch to detailed tips view');
        }
    });

    // Tips filtering functionality
    $('.filter-btn').on('click', function() {
        var $button = $(this);
        var filter = $button.data('filter');

        // Update active filter button
        $('.filter-btn').removeClass('active');
        $button.addClass('active');

        // Filter tips in quick view
        if (filter === 'all') {
            $('.tip-item').show();
        } else {
            $('.tip-item').hide();
            $('.tip-item[data-category="' + filter + '"]').show();
        }

        // Filter categories in detailed view
        if (filter === 'all') {
            $('.tip-category').show();
        } else {
            $('.tip-category').hide();
            $('.tip-category[data-category="' + filter + '"]').show();
        }

        console.log('CDN Tips - Filter applied:', filter);
    });

    // Category collapse/expand functionality
    $('.category-header').on('click', function() {
        var $header = $(this);
        var $category = $header.closest('.tip-category');
        var $tips = $category.find('.category-tips');

        if ($tips.is(':visible')) {
            $tips.slideUp(200);
            $header.addClass('collapsed');
        } else {
            $tips.slideDown(200);
            $header.removeClass('collapsed');
        }
    });



    // Initialize tips functionality
    function initializeTipsFeatures() {
        console.log('CDN Tips - Initializing enhanced tips features');

        // Set initial state
        $('#tips-quick-view').addClass('active');
        $('.filter-btn[data-filter="all"]').addClass('active');

        // Add hover effects for better UX
        $('.tip-item').on('mouseenter', function() {
            $(this).addClass('hovered');
        }).on('mouseleave', function() {
            $(this).removeClass('hovered');
        });

        // Add keyboard navigation
        $(document).on('keydown', function(e) {
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                $('#toggle-tips-view').click();
            }
        });

        console.log('CDN Tips - Enhanced tips features initialized');
    }

    // Initialize tips features
    setTimeout(initializeTipsFeatures, 300);
});
</script>
