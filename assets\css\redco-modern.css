/**
 * RedCo Optimizer - Clean Professional Interface
 * Inspired by <PERSON>e, Linear, and modern SaaS dashboards
 * Version: 6.0.0 - Universal Coverage
 */

/* ===== UNIVERSAL FOUNDATION ===== */
.redco-optimizer-modules,
.redco-optimizer-admin,
.redco-module-settings,
.redco-optimizer-settings {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    background: #fafbfc !important;
    color: #1f2937 !important;
    line-height: 1.5 !important;
    -webkit-font-smoothing: antialiased !important;
}

.redco-optimizer-modules .wrap,
.redco-optimizer-admin .wrap,
.redco-module-settings .wrap,
.redco-optimizer-settings .wrap {
    background: #fafbfc !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
}

/* ===== UNIVERSAL CLEAN LAYOUT ===== */
.redco-optimizer-modules,
.redco-optimizer-admin,
.redco-module-settings,
.redco-optimizer-settings {
    background: #fafbfc !important;
    padding: 32px !important;
    min-height: 100vh !important;
}

/* Create clean header for all pages */
.redco-optimizer-modules::before,
.redco-optimizer-admin::before,
.redco-module-settings::before,
.redco-optimizer-settings::before {
    content: '';
    position: fixed !important;
    top: 32px !important;
    left: 160px !important;
    right: 0 !important;
    height: 80px !important;
    background: white !important;
    border-bottom: 1px solid #e5e7eb !important;
    z-index: 1000 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.redco-optimizer-modules::after,
.redco-optimizer-admin::after,
.redco-module-settings::after,
.redco-optimizer-settings::after {
    content: '⚡ RedCo Optimizer';
    position: fixed !important;
    top: 32px !important;
    left: 160px !important;
    right: 0 !important;
    height: 80px !important;
    display: flex !important;
    align-items: center !important;
    padding: 0 32px !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    z-index: 1001 !important;
    background: white !important;
}

/* Push content below header for all pages */
.redco-optimizer-modules > *,
.redco-optimizer-admin > *,
.redco-module-settings > *,
.redco-optimizer-settings > * {
    margin-top: 100px !important;
}

/* ===== UNIVERSAL CLEAN CARDS ===== */
.redco-optimizer-modules .module-card,
.redco-optimizer-modules .modules-section,
.redco-optimizer-admin .redco-card,
.redco-optimizer-admin .form-table,
.redco-module-settings .form-table,
.redco-module-settings > div,
.redco-optimizer-settings .form-table,
.redco-optimizer-settings > div {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    margin: 0 0 24px 0 !important;
    padding: 24px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: box-shadow 0.2s ease !important;
}

.redco-optimizer-modules .module-card:hover,
.redco-optimizer-modules .modules-section:hover,
.redco-optimizer-admin .redco-card:hover,
.redco-optimizer-admin .form-table:hover,
.redco-module-settings .form-table:hover,
.redco-module-settings > div:hover,
.redco-optimizer-settings .form-table:hover,
.redco-optimizer-settings > div:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* ===== UNIVERSAL CLEAN TYPOGRAPHY ===== */
.redco-optimizer-modules h1,
.redco-optimizer-modules h2,
.redco-optimizer-modules h3,
.redco-optimizer-modules h4,
.redco-optimizer-admin h1,
.redco-optimizer-admin h2,
.redco-optimizer-admin h3,
.redco-optimizer-admin h4,
.redco-module-settings h1,
.redco-module-settings h2,
.redco-module-settings h3,
.redco-module-settings h4,
.redco-optimizer-settings h1,
.redco-optimizer-settings h2,
.redco-optimizer-settings h3,
.redco-optimizer-settings h4 {
    color: #1f2937 !important;
    font-weight: 600 !important;
    margin-bottom: 16px !important;
    line-height: 1.3 !important;
}

.redco-optimizer-modules h2,
.redco-optimizer-admin h2,
.redco-module-settings h2,
.redco-optimizer-settings h2 {
    font-size: 20px !important;
    color: #111827 !important;
    margin-bottom: 20px !important;
}

.redco-optimizer-modules h3,
.redco-optimizer-admin h3,
.redco-module-settings h3,
.redco-optimizer-settings h3 {
    font-size: 18px !important;
    color: #374151 !important;
}

.redco-optimizer-modules p,
.redco-optimizer-modules label,
.redco-optimizer-admin p,
.redco-optimizer-admin label,
.redco-module-settings p,
.redco-module-settings label,
.redco-optimizer-settings p,
.redco-optimizer-settings label {
    color: #4b5563 !important;
    line-height: 1.6 !important;
    margin-bottom: 12px !important;
}

.redco-optimizer-modules .description,
.redco-optimizer-admin .description,
.redco-module-settings .description,
.redco-optimizer-settings .description {
    color: #6b7280 !important;
    font-size: 14px !important;
    margin-top: 6px !important;
    line-height: 1.5 !important;
}

/* ===== UNIVERSAL CLEAN FORM ELEMENTS ===== */
.redco-optimizer-modules input[type="text"],
.redco-optimizer-modules input[type="number"],
.redco-optimizer-modules input[type="email"],
.redco-optimizer-modules select,
.redco-optimizer-modules textarea,
.redco-optimizer-admin input[type="text"],
.redco-optimizer-admin input[type="number"],
.redco-optimizer-admin input[type="email"],
.redco-optimizer-admin select,
.redco-optimizer-admin textarea,
.redco-module-settings input[type="text"],
.redco-module-settings input[type="number"],
.redco-module-settings input[type="email"],
.redco-module-settings select,
.redco-module-settings textarea,
.redco-optimizer-settings input[type="text"],
.redco-optimizer-settings input[type="number"],
.redco-optimizer-settings input[type="email"],
.redco-optimizer-settings select,
.redco-optimizer-settings textarea {
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    color: #1f2937 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* Focus states for all form elements */
.redco-optimizer-modules input[type="text"]:focus,
.redco-optimizer-modules input[type="number"]:focus,
.redco-optimizer-modules input[type="email"]:focus,
.redco-optimizer-modules select:focus,
.redco-optimizer-modules textarea:focus,
.redco-optimizer-admin input[type="text"]:focus,
.redco-optimizer-admin input[type="number"]:focus,
.redco-optimizer-admin input[type="email"]:focus,
.redco-optimizer-admin select:focus,
.redco-optimizer-admin textarea:focus,
.redco-module-settings input[type="text"]:focus,
.redco-module-settings input[type="number"]:focus,
.redco-module-settings input[type="email"]:focus,
.redco-module-settings select:focus,
.redco-module-settings textarea:focus,
.redco-optimizer-settings input[type="text"]:focus,
.redco-optimizer-settings input[type="number"]:focus,
.redco-optimizer-settings input[type="email"]:focus,
.redco-optimizer-settings select:focus,
.redco-optimizer-settings textarea:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

/* ===== UNIVERSAL CLEAN BUTTONS ===== */
.redco-optimizer-modules .button-primary,
.redco-optimizer-admin .button-primary,
.redco-module-settings .button-primary,
.redco-optimizer-settings .button-primary {
    background: #3b82f6 !important;
    border: none !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.redco-optimizer-modules .button-primary:hover,
.redco-optimizer-admin .button-primary:hover,
.redco-module-settings .button-primary:hover,
.redco-optimizer-settings .button-primary:hover {
    background: #2563eb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.redco-optimizer-modules .button-secondary,
.redco-optimizer-admin .button-secondary,
.redco-module-settings .button-secondary,
.redco-optimizer-settings .button-secondary {
    background: white !important;
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
}

.redco-optimizer-modules .button-secondary:hover,
.redco-optimizer-admin .button-secondary:hover,
.redco-module-settings .button-secondary:hover,
.redco-optimizer-settings .button-secondary:hover {
    border-color: #9ca3af !important;
    background: #f9fafb !important;
}

/* ===== UNIVERSAL CLEAN FORM TABLES ===== */
.redco-optimizer-modules .form-table,
.redco-optimizer-admin .form-table,
.redco-module-settings .form-table,
.redco-optimizer-settings .form-table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
    width: 100% !important;
}

.redco-optimizer-modules .form-table th,
.redco-optimizer-admin .form-table th,
.redco-module-settings .form-table th,
.redco-optimizer-settings .form-table th {
    background: #f9fafb !important;
    padding: 16px 20px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    border-bottom: 1px solid #e5e7eb !important;
    border-right: 1px solid #e5e7eb !important;
    width: 200px !important;
    vertical-align: top !important;
    font-size: 14px !important;
}

.redco-optimizer-modules .form-table td,
.redco-optimizer-admin .form-table td,
.redco-module-settings .form-table td,
.redco-optimizer-settings .form-table td {
    background: white !important;
    padding: 16px 20px !important;
    border-bottom: 1px solid #e5e7eb !important;
    vertical-align: top !important;
}

.redco-optimizer-modules .form-table tr:last-child th,
.redco-optimizer-modules .form-table tr:last-child td,
.redco-optimizer-admin .form-table tr:last-child th,
.redco-optimizer-admin .form-table tr:last-child td,
.redco-module-settings .form-table tr:last-child th,
.redco-module-settings .form-table tr:last-child td,
.redco-optimizer-settings .form-table tr:last-child th,
.redco-optimizer-settings .form-table tr:last-child td {
    border-bottom: none !important;
}

/* ===== UNIVERSAL CLEAN CHECKBOXES ===== */
.redco-optimizer-modules input[type="checkbox"],
.redco-optimizer-admin input[type="checkbox"],
.redco-module-settings input[type="checkbox"],
.redco-optimizer-settings input[type="checkbox"] {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    accent-color: #3b82f6 !important;
}

/* ===== HIDE WORDPRESS CLUTTER ===== */
.redco-optimizer-modules .wp-header-end,
.redco-optimizer-modules .screen-reader-text,
.redco-optimizer-modules .update-nag,
.redco-optimizer-admin .wp-header-end,
.redco-optimizer-admin .screen-reader-text,
.redco-optimizer-admin .update-nag,
.redco-module-settings .wp-header-end,
.redco-module-settings .screen-reader-text,
.redco-module-settings .update-nag,
.redco-optimizer-settings .wp-header-end,
.redco-optimizer-settings .screen-reader-text,
.redco-optimizer-settings .update-nag {
    display: none !important;
}

/* ===== UNIVERSAL CLEAN NOTICES ===== */
.redco-optimizer-modules .notice,
.redco-optimizer-modules .updated,
.redco-optimizer-modules .error,
.redco-optimizer-admin .notice,
.redco-optimizer-admin .updated,
.redco-optimizer-admin .error,
.redco-module-settings .notice,
.redco-module-settings .updated,
.redco-module-settings .error,
.redco-optimizer-settings .notice,
.redco-optimizer-settings .updated,
.redco-optimizer-settings .error {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-left: 4px solid #3b82f6 !important;
    border-radius: 8px !important;
    margin: 0 0 20px 0 !important;
    padding: 12px 16px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    color: #374151 !important;
}

.redco-optimizer-modules .notice.notice-success,
.redco-optimizer-modules .updated,
.redco-optimizer-admin .notice.notice-success,
.redco-optimizer-admin .updated,
.redco-module-settings .notice.notice-success,
.redco-module-settings .updated,
.redco-optimizer-settings .notice.notice-success,
.redco-optimizer-settings .updated {
    border-left-color: #10b981 !important;
}

.redco-optimizer-modules .notice.notice-error,
.redco-optimizer-modules .error,
.redco-optimizer-admin .notice.notice-error,
.redco-optimizer-admin .error,
.redco-module-settings .notice.notice-error,
.redco-module-settings .error,
.redco-optimizer-settings .notice.notice-error,
.redco-optimizer-settings .error {
    border-left-color: #ef4444 !important;
}

/* ===== UNIVERSAL CLEAN LINKS ===== */
.redco-optimizer-modules a,
.redco-optimizer-admin a,
.redco-module-settings a,
.redco-optimizer-settings a {
    color: #3b82f6 !important;
    text-decoration: none !important;
    transition: color 0.2s ease !important;
}

.redco-optimizer-modules a:hover,
.redco-optimizer-admin a:hover,
.redco-module-settings a:hover,
.redco-optimizer-settings a:hover {
    color: #2563eb !important;
    text-decoration: underline !important;
}

/* ===== UNIVERSAL RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .redco-optimizer-modules,
    .redco-optimizer-admin,
    .redco-module-settings,
    .redco-optimizer-settings {
        padding: 16px !important;
    }

    .redco-optimizer-modules::after,
    .redco-optimizer-admin::after,
    .redco-module-settings::after,
    .redco-optimizer-settings::after {
        font-size: 18px !important;
        padding: 0 16px !important;
    }

    .redco-optimizer-modules .form-table th,
    .redco-optimizer-modules .form-table td,
    .redco-optimizer-admin .form-table th,
    .redco-optimizer-admin .form-table td,
    .redco-module-settings .form-table th,
    .redco-module-settings .form-table td,
    .redco-optimizer-settings .form-table th,
    .redco-optimizer-settings .form-table td {
        padding: 12px !important;
        display: block !important;
        width: 100% !important;
        border-right: none !important;
    }
}


