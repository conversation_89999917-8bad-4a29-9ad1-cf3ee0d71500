/**
 * RedCo Optimizer - Revolutionary Modern UI
 * Inspired by the best admin interfaces: Notion, Linear, Vercel
 * Version: 3.0.0
 */

/* ===== COMPLETE RESET ===== */
.redco-optimizer-modules,
.redco-optimizer-modules *,
.redco-module-tab,
.redco-module-tab *,
.redco-module-settings,
.redco-module-settings * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* ===== GLOBAL FOUNDATION ===== */
.redco-optimizer-modules {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    background: #fafbfc;
    color: #1a1a1a;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.redco-optimizer-modules .wrap {
    background: #fafbfc !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
}

/* ===== STUNNING HEADER ===== */
.module-header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 200px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    margin: 0 -20px 0 -2px;
}

.module-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
}

.header-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 48px;
    display: flex;
    align-items: center;
    gap: 32px;
}

.header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-icon .dashicons {
    font-size: 40px;
    color: white;
}

.header-text h1 {
    font-size: 36px;
    font-weight: 700;
    color: white;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.02em;
}

.header-text p {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    opacity: 0.9;
}

/* ===== REVOLUTIONARY LAYOUT ===== */
.redco-module-tab {
    background: #fafbfc;
    min-height: 100vh;
}

.redco-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 380px;
    gap: 40px;
    padding: 40px 48px;
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== MAIN CONTENT AREA ===== */
.redco-content-main {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* ===== MODERN CARDS ===== */
.redco-card {
    background: white;
    border-radius: 16px;
    border: 1px solid #e6e8eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.redco-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
}

.redco-card-header {
    padding: 24px 28px;
    border-bottom: 1px solid #f3f4f6;
    background: #fafbfc;
}

.redco-card-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-card-header .dashicons {
    font-size: 20px;
    color: #667eea;
}

.redco-card-content {
    padding: 28px;
}

/* ===== SIDEBAR PERFECTION ===== */
.redco-content-sidebar {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.redco-sidebar-section {
    background: white;
    border-radius: 16px;
    border: 1px solid #e6e8eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.sidebar-section-header {
    padding: 20px 24px;
    background: #fafbfc;
    border-bottom: 1px solid #f3f4f6;
}

.sidebar-section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-section-header .dashicons {
    font-size: 18px;
    color: #667eea;
}

.sidebar-section-content {
    padding: 24px;
}

/* ===== BEAUTIFUL STATS ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 20px 16px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e6e8eb;
    transition: all 0.2s ease;
}

.stat-item:hover {
    background: #f1f5f9;
    border-color: #667eea;
    transform: translateY(-1px);
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 4px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== MODERN BUTTONS ===== */
.button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2) !important;
}

.button-primary:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.button-secondary {
    background: white !important;
    border: 1px solid #e6e8eb !important;
    color: #374151 !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
}

.button-secondary:hover {
    border-color: #667eea !important;
    color: #667eea !important;
    transform: translateY(-1px) !important;
}

/* ===== FORM ELEMENTS ===== */
input[type="text"],
input[type="number"],
input[type="email"],
select,
textarea {
    border: 1px solid #e6e8eb !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    background: white !important;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .redco-content-wrapper {
        grid-template-columns: 1fr;
        gap: 32px;
    }
    
    .redco-content-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 24px;
        flex-direction: column;
        text-align: center;
        gap: 24px;
    }

    .redco-content-wrapper {
        padding: 24px;
        gap: 24px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== COMPLETE LAYOUT TRANSFORMATION ===== */
/* Override ALL existing WordPress admin styles */
.redco-module-tab .form-table {
    background: white !important;
    border-radius: 16px !important;
    border: 1px solid #e6e8eb !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    margin: 0 !important;
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    overflow: hidden !important;
}

.redco-module-tab .form-table th {
    background: #fafbfc !important;
    padding: 20px 24px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    border-bottom: 1px solid #f3f4f6 !important;
    border-right: 1px solid #f3f4f6 !important;
    width: 200px !important;
    vertical-align: top !important;
}

.redco-module-tab .form-table td {
    background: white !important;
    padding: 20px 24px !important;
    border-bottom: 1px solid #f3f4f6 !important;
    vertical-align: top !important;
}

.redco-module-tab .form-table tr:last-child th,
.redco-module-tab .form-table tr:last-child td {
    border-bottom: none !important;
}

/* Transform existing content sections */
.redco-module-tab > div:not(.module-header-section):not(.redco-content-wrapper) {
    background: white !important;
    border-radius: 16px !important;
    border: 1px solid #e6e8eb !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    margin: 0 0 24px 0 !important;
    padding: 28px !important;
}

/* Style headings beautifully */
.redco-module-tab h1,
.redco-module-tab h2,
.redco-module-tab h3,
.redco-module-tab h4 {
    color: #1a1a1a !important;
    font-weight: 600 !important;
    margin-bottom: 16px !important;
    line-height: 1.3 !important;
}

.redco-module-tab h2 {
    font-size: 20px !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.redco-module-tab h3 {
    font-size: 18px !important;
}

/* Beautiful text styling */
.redco-module-tab p,
.redco-module-tab label {
    color: #374151 !important;
    line-height: 1.6 !important;
    margin-bottom: 12px !important;
}

.redco-module-tab .description {
    color: #6b7280 !important;
    font-size: 14px !important;
    margin-top: 6px !important;
    line-height: 1.5 !important;
}

/* Clean up notices */
.redco-module-tab .notice,
.redco-module-tab .updated,
.redco-module-tab .error {
    border-radius: 12px !important;
    border-left-width: 4px !important;
    margin: 0 0 24px 0 !important;
    padding: 16px 20px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Hide WordPress clutter */
.redco-module-tab .wp-header-end,
.redco-module-tab .screen-reader-text {
    display: none !important;
}

/* Force clean spacing */
.redco-module-tab > * {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Beautiful checkboxes */
.redco-module-tab input[type="checkbox"] {
    width: 18px !important;
    height: 18px !important;
    margin-right: 12px !important;
    accent-color: #667eea !important;
}

/* Clean lists */
.redco-module-tab ul,
.redco-module-tab ol {
    margin-left: 20px !important;
    margin-bottom: 16px !important;
}

.redco-module-tab li {
    margin-bottom: 8px !important;
    color: #374151 !important;
}
