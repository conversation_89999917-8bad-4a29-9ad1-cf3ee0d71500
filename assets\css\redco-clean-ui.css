/**
 * RedCo Optimizer - Clean UI System
 * Fresh, professional styling for all modules
 * Version: 1.0.0
 */

/* ===== RESET & BASE ===== */
.redco-module-tab {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    line-height: 1.6;
    color: #1e293b;
}

/* ===== MODULE HEADER ===== */
.module-header-section {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    margin: 0 -20px 30px -2px;
    padding: 0;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    min-height: 182px;
    position: relative;
    overflow: hidden;
}

.module-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 40px 20px 50px;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    align-items: start;
    position: relative;
    z-index: 1;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-icon .dashicons {
    font-size: 32px;
    color: white;
}

.header-text h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-text p {
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
}

/* ===== CONTENT LAYOUT ===== */
.redco-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 30px;
    margin-top: 0;
}

.redco-content-main {
    min-width: 0;
}

.redco-content-sidebar {
    background: #f8fafc;
    border-radius: 12px;
    padding: 0;
    border: 1px solid #e2e8f0;
    height: fit-content;
    position: sticky;
    top: 32px;
}

/* ===== SIDEBAR SECTIONS ===== */
.redco-sidebar-section {
    border-bottom: 1px solid #e2e8f0;
}

.redco-sidebar-section:last-child {
    border-bottom: none;
}

.sidebar-section-header {
    padding: 20px 24px 16px;
    background: #ffffff;
    border-bottom: 1px solid #f1f5f9;
}

.sidebar-section-header:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.sidebar-section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-section-header .dashicons {
    font-size: 18px;
    color: #4CAF50;
}

.sidebar-section-content {
    padding: 20px 24px;
    background: #ffffff;
}

.sidebar-section:last-child .sidebar-section-content {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

/* ===== STATS GRID ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #4CAF50;
    margin-bottom: 4px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== CARDS ===== */
.redco-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.redco-card-header {
    padding: 24px 28px 20px;
    border-bottom: 1px solid #f1f5f9;
    background: #ffffff;
}

.redco-card-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 10px;
}

.redco-card-header .dashicons {
    font-size: 20px;
    color: #4CAF50;
}

.redco-card-content {
    padding: 28px;
}

/* ===== BUTTONS ===== */
.button-primary {
    background: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    font-weight: 500;
    border-radius: 6px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.button-primary:hover {
    background: #388E3C !important;
    border-color: #388E3C !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.button-secondary {
    background: white !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
    font-weight: 500;
    border-radius: 6px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.button-secondary:hover {
    border-color: #4CAF50 !important;
    color: #4CAF50 !important;
}

/* ===== FORM ELEMENTS ===== */
.form-table th {
    font-weight: 600;
    color: #374151;
    padding: 16px 0;
}

.form-table td {
    padding: 16px 0;
}

input[type="text"],
input[type="number"],
select,
textarea {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

input[type="text"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    outline: none;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1200px) {
    .redco-content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .redco-content-sidebar {
        order: -1;
        position: static;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 20px;
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .header-main {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
