/**
 * Standardized Module UI Framework for Redco Optimizer
 * 
 * Provides consistent visual design across all modules with:
 * - Standardized 182px module headers
 * - Consistent button styling and placement
 * - Unified icon usage (dashicons only)
 * - Standardized stats display format
 * - Clean card layouts without section descriptions
 */

/* ===== MODULE HEADER STANDARDIZATION ===== */

.redco-module-header {
    height: 182px !important;
    min-height: 182px !important;
    max-height: 182px !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 8px 8px 0 0;
    position: relative;
    overflow: hidden;
}

.redco-module-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.module-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
    z-index: 2;
}

.module-header-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.module-icon {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
}

.module-title-section h1 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: white;
    line-height: 1.2;
}

.module-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
}

.module-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
}

.module-status-badge.enabled {
    background: rgba(76, 175, 80, 0.2);
    color: #E8F5E8;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.module-status-badge.disabled {
    background: rgba(244, 67, 54, 0.2);
    color: #FFEBEE;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* ===== HEADER ACTIONS STANDARDIZATION ===== */

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-metrics {
    display: flex;
    gap: 20px;
    align-items: center;
}

.header-metric {
    text-align: center;
    min-width: 80px;
}

.header-metric-value {
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
    color: white;
}

.header-metric-label {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-quick-actions {
    display: flex;
    gap: 10px;
}

.header-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.header-action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.header-action-btn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

.header-action-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* ===== BUTTON STANDARDIZATION ===== */

.redco-btn,
.button.redco-btn,
input[type="submit"].redco-btn,
input[type="button"].redco-btn,
.wp-core-ui .button.redco-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1.4;
    box-shadow: none;
    text-shadow: none;
}

.redco-btn-primary {
    background: #4CAF50;
    color: white;
    border: 1px solid #4CAF50;
}

.redco-btn-primary:hover {
    background: #45a049;
    border-color: #45a049;
    color: white;
    transform: translateY(-1px);
}

.redco-btn-secondary {
    background: white;
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.redco-btn-secondary:hover {
    background: #f8f9fa;
    color: #388E3C;
    border-color: #388E3C;
}

.redco-btn-danger {
    background: #f44336;
    color: white;
    border: 1px solid #f44336;
}

.redco-btn-danger:hover {
    background: #d32f2f;
    border-color: #d32f2f;
    color: white;
}

.redco-btn-outline {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
}

.redco-btn-outline:hover {
    background: #f8f9fa;
    border-color: #4CAF50;
    color: #4CAF50;
}

.redco-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Ensure white dashicons on green buttons */
.redco-btn-primary .dashicons,
.header-action-btn .dashicons,
.button-primary .dashicons,
.button.button-primary .dashicons,
[style*="background:#4CAF50"] .dashicons,
[style*="background: #4CAF50"] .dashicons,
[style*="background-color:#4CAF50"] .dashicons,
[style*="background-color: #4CAF50"] .dashicons {
    color: white !important;
}

.redco-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* WordPress core button overrides for consistency */
.wp-core-ui .button-primary {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.wp-core-ui .button-primary:hover {
    background: #45a049;
    border-color: #45a049;
    color: white;
}

.wp-core-ui .button-primary .dashicons {
    color: white !important;
}

/* Standardize all module buttons */
.redco-module-tab .button,
.redco-module-content .button,
.redco-card .button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.redco-module-tab .button:hover,
.redco-module-content .button:hover,
.redco-card .button:hover {
    transform: translateY(-1px);
}

/* ===== CARD LAYOUT STANDARDIZATION ===== */

.redco-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: block !important;
    visibility: visible !important;
}

.redco-card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.redco-card-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.redco-card-title .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: #4CAF50;
}

.redco-card-content {
    padding: 20px;
}

/* Remove section descriptions for cleaner UI */
.redco-card-description,
.card-description,
.section-description {
    display: none !important;
}

/* Keep settings-intro visible but clean */
.settings-intro {
    margin-bottom: 16px;
}

/* Ensure module content is visible */
.redco-module-content,
.redco-content-main,
.module-layout,
.redco-module-form,
.redco-card,
.card-content,
.settings-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Debug: Ensure forms and cards are not hidden */
form.redco-module-form {
    display: block !important;
    visibility: visible !important;
    min-height: 100px;
    background: white;
}

/* Debug: Ensure card content is visible */
.redco-card .card-content {
    display: block !important;
    padding: 20px !important;
    min-height: 50px;
    background: #f0f0f0 !important; /* Temporary debug background */
    border: 2px solid red !important; /* Temporary debug border */
}

/* Debug: Make module content highly visible */
.redco-module-content {
    background: yellow !important; /* Temporary debug background */
    min-height: 200px !important;
    border: 3px solid blue !important; /* Temporary debug border */
}

/* Debug: Make forms visible */
.redco-module-form {
    background: lightgreen !important; /* Temporary debug background */
    min-height: 100px !important;
    border: 2px solid purple !important; /* Temporary debug border */
}

/* ===== STATS DISPLAY STANDARDIZATION ===== */

.redco-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 20px 0;
}

.redco-stat-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.2s ease;
}

.redco-stat-item:hover {
    border-color: #4CAF50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.redco-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #4CAF50;
    line-height: 1;
    margin-bottom: 4px;
}

.redco-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-stat-icon {
    font-size: 20px;
    color: #4CAF50;
    margin-bottom: 8px;
}

/* ===== SIDEBAR STANDARDIZATION ===== */

.redco-sidebar-section {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.redco-sidebar-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.redco-sidebar-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.redco-sidebar-header .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #4CAF50;
}

.redco-sidebar-content {
    padding: 16px;
}

/* ===== FORM ELEMENTS STANDARDIZATION ===== */

.redco-form-group {
    margin-bottom: 20px;
}

.redco-form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.redco-form-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #4CAF50;
}

.redco-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.redco-form-control:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.redco-form-help {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    font-style: italic;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .redco-module-header {
        height: auto !important;
        min-height: 140px !important;
        max-height: none !important;
        padding: 16px 20px;
    }
    
    .module-header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .module-header-info {
        flex-direction: column;
        gap: 12px;
    }
    
    .module-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
    }
    
    .module-title-section h1 {
        font-size: 24px;
    }
    
    .header-metrics {
        flex-wrap: wrap;
        gap: 12px;
        justify-content: center;
    }
    
    .header-quick-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .redco-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }
}
