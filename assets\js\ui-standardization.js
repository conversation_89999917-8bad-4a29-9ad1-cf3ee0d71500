/**
 * UI Standardization Script for Redco Optimizer
 * 
 * Automatically applies standardized classes and styling to ensure
 * consistent UI across all modules and components.
 */

(function($) {
    'use strict';

    const RedcoUIStandardization = {
        
        // Initialize UI standardization
        init: function() {
            this.standardizeButtons();
            this.standardizeCards();
            this.standardizeIcons();
            this.standardizeForms();
            this.standardizeStats();
            this.ensureResponsiveDesign();
            this.removeVisualClutter();
        },

        // Standardize all buttons across modules
        standardizeButtons: function() {
            // Add redco-btn class to all buttons in module areas
            $('.redco-module-tab .button, .redco-module-content .button, .redco-card .button').each(function() {
                const $btn = $(this);
                
                // Don't modify if already has redco-btn class
                if ($btn.hasClass('redco-btn')) {
                    return;
                }
                
                $btn.addClass('redco-btn');
                
                // Determine button type based on existing classes
                if ($btn.hasClass('button-primary') || $btn.hasClass('primary')) {
                    $btn.addClass('redco-btn-primary');
                } else if ($btn.hasClass('button-secondary') || $btn.hasClass('secondary')) {
                    $btn.addClass('redco-btn-secondary');
                } else if ($btn.hasClass('delete') || $btn.text().toLowerCase().includes('delete') || 
                          $btn.text().toLowerCase().includes('remove') || $btn.text().toLowerCase().includes('clear')) {
                    $btn.addClass('redco-btn-danger');
                } else {
                    $btn.addClass('redco-btn-outline');
                }
                
                // Ensure dashicons are present and properly styled
                this.ensureButtonIcon($btn);
            }.bind(this));

            // Standardize header action buttons
            $('.header-action-btn, .header-quick-actions .button').each(function() {
                const $btn = $(this);
                if (!$btn.find('.dashicons').length) {
                    this.ensureButtonIcon($btn);
                }
            }.bind(this));
        },

        // Ensure buttons have appropriate dashicons
        ensureButtonIcon: function($btn) {
            if ($btn.find('.dashicons').length > 0) {
                return; // Already has icon
            }

            const text = $btn.text().toLowerCase();
            let iconClass = '';

            // Determine appropriate icon based on button text
            if (text.includes('save') || text.includes('update')) {
                iconClass = 'dashicons-yes-alt';
            } else if (text.includes('delete') || text.includes('remove')) {
                iconClass = 'dashicons-trash';
            } else if (text.includes('clear') || text.includes('reset')) {
                iconClass = 'dashicons-undo';
            } else if (text.includes('optimize') || text.includes('performance')) {
                iconClass = 'dashicons-performance';
            } else if (text.includes('test') || text.includes('diagnose')) {
                iconClass = 'dashicons-admin-tools';
            } else if (text.includes('refresh') || text.includes('reload')) {
                iconClass = 'dashicons-update';
            } else if (text.includes('download') || text.includes('export')) {
                iconClass = 'dashicons-download';
            } else if (text.includes('upload') || text.includes('import')) {
                iconClass = 'dashicons-upload';
            } else if (text.includes('settings') || text.includes('configure')) {
                iconClass = 'dashicons-admin-generic';
            } else if (text.includes('help') || text.includes('support')) {
                iconClass = 'dashicons-sos';
            } else if (text.includes('view') || text.includes('show')) {
                iconClass = 'dashicons-visibility';
            } else {
                iconClass = 'dashicons-admin-generic'; // Default icon
            }

            // Prepend icon to button
            $btn.prepend(`<span class="dashicons ${iconClass}"></span> `);
        },

        // Standardize card layouts
        standardizeCards: function() {
            // Ensure all cards have proper structure
            $('.redco-card').each(function() {
                const $card = $(this);
                
                // Ensure card has proper header structure
                let $header = $card.find('.card-header, .redco-card-header').first();
                if ($header.length === 0) {
                    $header = $card.find('h2, h3, h4').first().parent();
                    if ($header.length > 0) {
                        $header.addClass('redco-card-header');
                    }
                }
                
                // Ensure header has icon
                if ($header.length > 0) {
                    const $title = $header.find('h2, h3, h4').first();
                    if ($title.length > 0 && !$title.find('.dashicons').length) {
                        $title.addClass('redco-card-title');
                        $title.prepend('<span class="dashicons dashicons-admin-generic"></span> ');
                    }
                }
            });
        },

        // Standardize icons across the interface
        standardizeIcons: function() {
            // Ensure all dashicons in green buttons are white
            $('.redco-btn-primary .dashicons, .button-primary .dashicons, .header-action-btn .dashicons').css('color', 'white');
            
            // Standardize icon sizes
            $('.redco-card-title .dashicons').css({
                'font-size': '18px',
                'width': '18px',
                'height': '18px',
                'color': '#4CAF50'
            });
            
            $('.redco-sidebar-header .dashicons').css({
                'font-size': '16px',
                'width': '16px',
                'height': '16px',
                'color': '#4CAF50'
            });
        },

        // Standardize form elements
        standardizeForms: function() {
            // Add redco-form-control class to inputs
            $('.redco-module-content input[type="text"], .redco-module-content input[type="number"], .redco-module-content select, .redco-module-content textarea').each(function() {
                const $input = $(this);
                if (!$input.hasClass('redco-form-control')) {
                    $input.addClass('redco-form-control');
                }
            });

            // Standardize form labels
            $('.redco-module-content label').each(function() {
                const $label = $(this);
                if (!$label.hasClass('redco-form-label') && !$label.closest('.option-checkbox-item').length) {
                    $label.addClass('redco-form-label');
                    
                    // Add icon if not present
                    if (!$label.find('.dashicons').length) {
                        $label.prepend('<span class="dashicons dashicons-admin-generic"></span> ');
                    }
                }
            });
        },

        // Standardize statistics display
        standardizeStats: function() {
            // Convert existing stats to standardized format
            $('.stat-item, .header-metric').each(function() {
                const $stat = $(this);
                
                if (!$stat.hasClass('redco-stat-item') && !$stat.hasClass('header-metric')) {
                    $stat.addClass('redco-stat-item');
                }
                
                // Ensure proper structure
                const $value = $stat.find('.stat-value, .header-metric-value').first();
                const $label = $stat.find('.stat-label, .header-metric-label').first();
                
                if ($value.length > 0) {
                    $value.addClass('redco-stat-value');
                }
                if ($label.length > 0) {
                    $label.addClass('redco-stat-label');
                }
            });
        },

        // Ensure responsive design elements
        ensureResponsiveDesign: function() {
            // Add responsive classes to module headers
            $('.module-header-section, .redco-module-header').addClass('responsive-header');
            
            // Add responsive classes to content areas
            $('.redco-module-content').addClass('responsive-content');
            
            // Add responsive classes to sidebars
            $('.redco-content-sidebar').addClass('responsive-sidebar');
        },

        // Remove visual clutter as per user preferences
        removeVisualClutter: function() {
            // Hide section descriptions for cleaner UI (but not settings-intro)
            $('.redco-card-description, .card-description, .section-description').hide();

            // Remove excessive spacing
            $('.redco-card .card-content').css('padding-top', '16px');

            // Remove hover effects from stat items as requested
            $('.stat-item, .redco-stat-item').css('transition', 'none');
            $('.stat-item:hover, .redco-stat-item:hover').css({
                'border-color': '#e0e0e0',
                'box-shadow': 'none',
                'transform': 'none'
            });

            // Remove left borders from specific elements
            $('.exclusion-intro, .minification-option, .emoji-option, .version-option, .advanced-option').css('border-left', 'none');
        },

        // Apply consistent spacing and layout
        applyConsistentLayout: function() {
            // Ensure consistent module header height
            $('.module-header-section, .redco-module-header').css({
                'min-height': '182px',
                'height': '182px'
            });
            
            // Ensure sidebar height matches content
            this.matchSidebarHeight();
        },

        // Match sidebar height to content area
        matchSidebarHeight: function() {
            $('.module-layout').each(function() {
                const $layout = $(this);
                const $main = $layout.find('.redco-content-main');
                const $sidebar = $layout.find('.redco-content-sidebar');
                
                if ($main.length && $sidebar.length) {
                    const mainHeight = $main.outerHeight();
                    $sidebar.css('min-height', mainHeight + 'px');
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only run on Redco Optimizer pages
        if ($('.redco-optimizer-admin, .redco-module-tab, .redco-module-content').length > 0) {
            RedcoUIStandardization.init();

            // Re-apply standardization after AJAX calls
            $(document).ajaxComplete(function() {
                setTimeout(() => {
                    RedcoUIStandardization.standardizeButtons();
                    RedcoUIStandardization.standardizeIcons();
                }, 100);
            });

            // Re-apply layout on window resize
            $(window).on('resize', function() {
                RedcoUIStandardization.matchSidebarHeight();
            });
        }
    });

    // Expose to global scope for external access
    window.RedcoUIStandardization = RedcoUIStandardization;

})(jQuery);
