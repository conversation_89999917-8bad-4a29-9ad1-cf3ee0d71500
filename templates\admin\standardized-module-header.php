<?php
/**
 * Standardized Module Header Template
 * 
 * Provides consistent 182px height headers across all modules with:
 * - Module icon, title, and status
 * - Performance metrics display
 * - Quick action buttons
 * - Responsive design
 * 
 * Variables expected:
 * - $module_key: Module identifier (e.g., 'lazy-load')
 * - $module_title: Display title (e.g., 'Lazy Load Images')
 * - $module_subtitle: Brief description
 * - $module_icon: Dashicon class (e.g., 'dashicons-visibility')
 * - $is_enabled: Boolean module status
 * - $stats: Array of statistics to display
 * - $quick_actions: Array of action buttons
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Default values if not provided
$module_key = $module_key ?? 'unknown';
$module_title = $module_title ?? 'Module';
$module_subtitle = $module_subtitle ?? 'Performance optimization module';
$module_icon = $module_icon ?? 'dashicons-admin-generic';
$is_enabled = $is_enabled ?? false;
$stats = $stats ?? array();
$quick_actions = $quick_actions ?? array();

// Default quick actions if none provided
if (empty($quick_actions) && $is_enabled) {
    $quick_actions = array(
        array(
            'id' => 'optimize-settings',
            'label' => __('Optimize', 'redco-optimizer'),
            'icon' => 'dashicons-performance',
            'class' => 'header-action-btn'
        ),
        array(
            'id' => 'view-diagnostics',
            'label' => __('Diagnose', 'redco-optimizer'),
            'icon' => 'dashicons-admin-tools',
            'url' => admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'),
            'class' => 'header-action-btn'
        )
    );
}

// Add "All Modules" link to all quick actions
$quick_actions[] = array(
    'id' => 'all-modules',
    'label' => __('All Modules', 'redco-optimizer'),
    'icon' => 'dashicons-admin-generic',
    'url' => admin_url('admin.php?page=redco-optimizer-modules'),
    'class' => 'header-action-btn'
);
?>

<div class="redco-module-header">
    <div class="module-header-content">
        <!-- Module Info Section -->
        <div class="module-header-info">
            <div class="module-icon">
                <span class="dashicons <?php echo esc_attr($module_icon); ?>"></span>
            </div>
            
            <div class="module-title-section">
                <h1><?php echo esc_html($module_title); ?></h1>
                <p class="module-subtitle"><?php echo esc_html($module_subtitle); ?></p>
                
                <div class="module-status-badge <?php echo $is_enabled ? 'enabled' : 'disabled'; ?>">
                    <span class="dashicons <?php echo $is_enabled ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                    <?php echo $is_enabled ? __('Active', 'redco-optimizer') : __('Inactive', 'redco-optimizer'); ?>
                </div>
            </div>
        </div>

        <!-- Header Actions Section -->
        <div class="header-actions">
            <!-- Performance Metrics -->
            <?php if ($is_enabled && !empty($stats)): ?>
                <div class="header-metrics">
                    <?php 
                    $metric_count = 0;
                    foreach ($stats as $key => $value): 
                        if ($metric_count >= 3) break; // Limit to 3 metrics for space
                        $metric_count++;
                        
                        // Format metric label
                        $label = ucwords(str_replace('_', ' ', $key));
                        if (strlen($label) > 10) {
                            $label = substr($label, 0, 10) . '...';
                        }
                        
                        // Format metric value
                        $display_value = $value;
                        if (is_numeric($value)) {
                            if ($value > 1000000) {
                                $display_value = round($value / 1000000, 1) . 'M';
                            } elseif ($value > 1000) {
                                $display_value = round($value / 1000, 1) . 'K';
                            } else {
                                $display_value = number_format($value);
                            }
                        }
                    ?>
                        <div class="header-metric">
                            <div class="header-metric-value"><?php echo esc_html($display_value); ?></div>
                            <div class="header-metric-label"><?php echo esc_html($label); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="header-quick-actions">
                <?php foreach ($quick_actions as $action): ?>
                    <?php if (isset($action['url'])): ?>
                        <a href="<?php echo esc_url($action['url']); ?>" 
                           class="<?php echo esc_attr($action['class'] ?? 'header-action-btn'); ?>"
                           <?php if (isset($action['id'])): ?>id="<?php echo esc_attr($action['id']); ?>"<?php endif; ?>>
                            <?php if (isset($action['icon'])): ?>
                                <span class="dashicons <?php echo esc_attr($action['icon']); ?>"></span>
                            <?php endif; ?>
                            <?php echo esc_html($action['label']); ?>
                        </a>
                    <?php else: ?>
                        <button type="button" 
                                class="<?php echo esc_attr($action['class'] ?? 'header-action-btn'); ?>"
                                <?php if (isset($action['id'])): ?>id="<?php echo esc_attr($action['id']); ?>"<?php endif; ?>>
                            <?php if (isset($action['icon'])): ?>
                                <span class="dashicons <?php echo esc_attr($action['icon']); ?>"></span>
                            <?php endif; ?>
                            <?php echo esc_html($action['label']); ?>
                        </button>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle optimize button click
    $('#optimize-settings').on('click', function(e) {
        e.preventDefault();
        
        const button = $(this);
        const originalText = button.text();
        
        // Prevent multiple clicks
        if (button.hasClass('loading')) {
            return;
        }
        
        button.addClass('loading')
              .html('<span class="dashicons dashicons-update-alt"></span> <?php _e("Optimizing...", "redco-optimizer"); ?>');
        
        // Apply optimal settings for this module
        const moduleKey = '<?php echo esc_js($module_key); ?>';
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_apply_optimal_settings',
                module: moduleKey,
                nonce: '<?php echo wp_create_nonce("redco_optimizer_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    if (typeof showToast === 'function') {
                        showToast('<?php _e("Settings optimized for maximum performance!", "redco-optimizer"); ?>', 'success');
                    }
                    
                    // Reload page to show updated settings
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    if (typeof showToast === 'function') {
                        showToast(response.data || '<?php _e("Failed to optimize settings", "redco-optimizer"); ?>', 'error');
                    }
                }
            },
            error: function() {
                if (typeof showToast === 'function') {
                    showToast('<?php _e("Network error occurred", "redco-optimizer"); ?>', 'error');
                }
            },
            complete: function() {
                // Reset button after delay
                setTimeout(() => {
                    button.removeClass('loading')
                          .html('<span class="dashicons dashicons-performance"></span> <?php _e("Optimize", "redco-optimizer"); ?>');
                }, 1000);
            }
        });
    });
    
    // Add hover effects to metrics
    $('.header-metric').hover(
        function() {
            $(this).css('transform', 'scale(1.05)');
        },
        function() {
            $(this).css('transform', 'scale(1)');
        }
    );
});
</script>

<style>
/* Module-specific header enhancements */
.header-metric {
    transition: transform 0.2s ease;
    cursor: default;
}

.header-action-btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

.header-action-btn.loading .dashicons {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments for header */
@media (max-width: 768px) {
    .header-metric-label {
        font-size: 10px;
    }
    
    .header-action-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .header-action-btn .dashicons {
        font-size: 14px;
        width: 14px;
        height: 14px;
    }
}
</style>
